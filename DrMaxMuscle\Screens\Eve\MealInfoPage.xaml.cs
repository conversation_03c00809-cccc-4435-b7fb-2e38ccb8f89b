﻿using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Controls;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Enums;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.User;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Networking;
using Newtonsoft.Json;
using System.Text;
using DrMaxMuscle.Screens.Subscription;
using RGPopup.Maui.Services;
using Microcharts;
using SkiaSharp;
using static System.Collections.Specialized.BitVector32;
using FFImageLoading;
using Rollbar;
using DrMaxMuscle.Utility;


namespace DrMaxMuscle.Screens.Eve;

public partial class MealInfoPage : ContentPage
{
    
    private CancellationTokenSource _cancellationTokenSource;
    
    private bool IsBodyweightPopup = false;
    decimal _hour;
    //IMealPlanService _mealPlanServices;
    //IMealServices _mealServices;
    //IUserService _userService;

    string LblWeightGoal = "";
    string CarbRecom = "";
    string ProteinRecom = "";
    string FatRecom = "";
    decimal _targetIntake = 0;
    decimal calculatedProteins = 0;
    decimal calculatedCarbs = 0;
    decimal calculatedFats = 0;

    decimal TotalKcal = 0;
    decimal TotalProteins = 0;
    IFirebase _firebase;

    CustomImageButton BtnKeto;
    CustomImageButton BtnPaleo;
    CustomImageButton BtnVegan;
    CustomImageButton BtnMediterranean;
    CustomImageButton BtnVegetarian;

    CustomImageButton BtnNoPreference;

    CustomImageButton Btn12Times;
    CustomImageButton Btn34Times;
    CustomImageButton Btn5PlusTimes;
    CustomImageButton BtnIdontExercise;

    CustomImageButton BtnIMostlySit;
    CustomImageButton BtnIMostlyStandWalk;
    CustomImageButton BtnIdoManualOrPhysicalWork;

    CustomImageButton BtnFish;
    CustomImageButton BtnEgg;
    CustomImageButton BtnDairyProduct;
    CustomImageButton BtnRedMeat;
    CustomImageButton BtnPoultry;
    CustomImageButton BtnNuts;
    CustomImageButton BtnBeans;
    CustomImageButton BtnTofu;
    CustomImageButton BtnSeafood;
    bool IsSeafood = false;

    Grid btnLog;
    Button btnBodyWeight;
    Button btnGetPlan;
    bool IsFish = false;
    bool IsEggs = false;
    bool IsDairy = false;
    bool IsRedMeat = false;
    bool IsPoultry = false;
    bool IsNuts = false;
    bool IsBeans = false;
    bool IsTofu = false;
    bool IsSimpleMealPlan = false;
    

    bool IsKetoSelected = false;
    bool IsPaleoSelected = false;
    bool IsVeganSelected = false;
    bool IsVegetarianSelected = false;
    bool IsMediterraneanSelected = false;
    bool IsNoPreferSelected = false;
    
    public string ExericseTime;
    public string ActiveOnJob;

    bool IsBreakfast = false;
    bool IsLunch = false;
    bool IsDinner = false;
    bool IsSnack1 = false;
    bool IsSnack2 = false;
    bool IsProteinShake = false;
    public string ApprovedMeals = "";

    string firstMealResponse = "";
    string secondMealResponse = "";
    string thirdMealResponse = "";
    string forthMealResponse = "";
    string fifthMealResponse = "";

    string currentResponse = "";
    string lastResultForGPT3 = "";
    string BFMealHeaderForGPT3 = "";
    string LunchMealHeaderForGPT3 = "";
    string DinnerMealHeaderForGPT3 = "";
    string S1MealHeaderForGPT3 = "";
    string S2MealHeaderForGPT3 = "";
    string PSMealHeaderForGPT3 = "";

    CustomImageButton BtnBreakfast;
    CustomImageButton BtnLunch;
    CustomImageButton BtnDinner;
    CustomImageButton BtnSnack1;
    CustomImageButton BtnSnack2;
    CustomImageButton BtnProteinShake;

    bool IsDefaultMeals = false;
    int index = 0;

    

    string roundedProsValue = "0";
    string roundedCarbsValue = "0";
    string roundedFatsValue = "0";

    
    bool isChangePopupSubscribed = false;
    double MinimumCalories = 0;
    double MaximumCalories = 0;

    bool IsFirstMealChange = false;
    bool isSecondMealChange = false;
    bool isThirdMealChange = false;
    bool isForthMealChange = false;
    bool isFifthMealChange = false;
    bool isSixthMealChange = false;

    private int currentDay = 1;

    string totalSources = "Red meat, Poultry, Fish, Eggs, Dairy, Nuts, Beans, Tofu (soy)";
    string unselectedSource = "";
    bool resetClicked = false;
    bool BreakLoop = false;
    bool IsOnbeforeCalled = false;
    private MainAIPage _mainPage;
    decimal currentCalories = 0, currentTargetIntake = 0, calDifference = 0;
    public MealInfoPage(MainAIPage page = null)
	{
		InitializeComponent();
        _mainPage = page;
        Title = "Meal plan";
        lstChats.ItemsSource = App.BotList;
        //App.BotList = new ObservableCollection<BotModel>();

        MessagingCenter.Subscribe<string>(this, "MealResponseGiven", (obj) =>
        {
            var botObj = App.BotList.FirstOrDefault(x => x.Type == BotType.MealSurveyCard);
            switch (obj)
            {
                case "Sad":
                    botObj.SelectedSurveyOption = SatisfactionSurveyEnum.Sad;
                    break;
                case "Neutral":
                    botObj.SelectedSurveyOption = SatisfactionSurveyEnum.Neutral;
                    break;
                case "Happy":
                    botObj.SelectedSurveyOption = SatisfactionSurveyEnum.Happy;
                    break;
                default:
                    botObj.SelectedSurveyOption = SatisfactionSurveyEnum.None;
                    break;
            }
            // Find the index of the survey card within App.BotList
            if (botObj != null)
            {
                int index = App.BotList.IndexOf(botObj);
                if(index >= 0 && index < App.BotList.Count)
                    App.BotList[index] = botObj;
            }

            MainThread.BeginInvokeOnMainThread(() =>
            {
                //lstChats.ItemsSource = null;
                //lstChats.ItemsSource = App.BotList;
                lstChats.ScrollTo(App.BotList.LastOrDefault(), ScrollToPosition.End, animate: false);
                lstChats.ScrollTo(App.BotList.LastOrDefault(), ScrollToPosition.End, animate: false);
            });

        });

         
        //_firebase = DependencyService.Get<IFirebase>();
        //DependencyService.Get<IDrMuscleSubcription>().OnMealPlanAccessPurchased += async delegate {
        //    App.IsMealPlan = true;
        //    if (Device.RuntimePlatform.Equals(Device.Android))
        //     UserDialogs.Instance.AlertAsync(new AlertConfig() { AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray), Message= "Your purchase was successful.", Title="You're all set", OkText = "OK" });
        //    App.BotList.Clear();
        //};
    }

    private async void RegenerateGPT3ResponseForEachBubble(string data)
    {
        if (string.IsNullOrEmpty(data))
            return;
        var header = "";
        data = data.Replace("...", "");
        //var ToSearch = "";
        //if (data.StartsWith("Protein Shake"))
        //{
        //    data = data.Replace("Protein Shake", "Protein shake:");
        //    header = "Protein shake:";
        //    ToSearch = "Protein Shake";
        //}
        //else
        header = await GetHeaderForReceipe(data);
        try
        {
            MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
        }
        catch (Exception ex)
        {

        }
        try
        {
            string[] lines = data.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            string firstLine = "";
            string remainingLines = "";
            // Check if there are at least two lines
            if (lines?.Length >= 1)
            {
                firstLine = string.Join(Environment.NewLine, lines, 0, 1);
                remainingLines = string.Join(Environment.NewLine, lines, 1, lines.Length - 1);
            }
            else
            {
            }
            var chatPopUp = new MealGeneralPopup();
            chatPopUp.SetPopupTitle("Edit meal", GeneralPopupEnum.ChangeMealPlan, "", remainingLines);
            if (chatPopUp != null)
            {
                await PopupNavigation.Instance.PushAsync(chatPopUp);
                var tcs = new TaskCompletionSource<string>();
                try
                {
                    MessagingCenter.Subscribe<Message.GeneralMessage>(this, "GeneralMessage", async (obj1) =>
                    {
                        //App.isChangePopupSubscribed = true;
                        if (obj1.IsCanceled)
                        {
                            return;
                        }
                        string messageFromPopup = "";
                        try
                        {
                            if (!string.IsNullOrEmpty(obj1.GeneralText))
                            {
                                tcs.TrySetResult(obj1.GeneralText);
                                messageFromPopup = await tcs.Task;
                            }

                        }
                        catch (Exception ex)
                        {
                            //RollbarLocator.RollbarInstance.Error(ex);
                        }
                        if (!string.IsNullOrEmpty(messageFromPopup) || !string.IsNullOrWhiteSpace(messageFromPopup))
                        {
                            try
                            {

                                /////
                                string newHeader = "";

                                newHeader = header;
                                newHeader = newHeader.ToLower();
                                if (newHeader.StartsWith("protein shake"))
                                {
                                    newHeader = "protein shake";
                                }


                                //if (header.StartsWith("Protein Shake"))
                                //{
                                //    newHeader = "Protein Shake";
                                //}
                                //else
                                //{
                                //    newHeader = header;
                                //}
                                newHeader = newHeader.Trim();
                                newHeader = newHeader.ToLower();
                                string keyName = "Day" + currentDay.ToString() + "_" + newHeader + "_Recipe";

                                LocalDBManager.Instance.SetDBSetting(keyName, "");




                                //var newHeader = header.Trim();
                                //newHeader = newHeader.ToLower();
                                //string keyName = "Day" + currentDay.ToString() + "_"+ newHeader + "_Recipe";

                                //LocalDBManager.Instance.SetDBSetting(keyName, "");

                                App.IsMealReponseLoaded = false;
                                var seperation = (Device.RuntimePlatform == Device.Android) ? "\r\n" : "\n";
                                var rearrangeData = firstLine + seperation + messageFromPopup;

                                //var modalPage = new Views.GeneralPopup("Lamp.png", "", "", "");
                                //modalPage = new Views.GeneralPopup("Medal.png", $"Success!", $"Your changes are coming up.", "Continue", null, false, false, "false", "false", "false", "false", "true", "false", "false", false, true, "", "Change Result");
                                //await PopupNavigation.Instance.PushAsync(modalPage);

                                //var finalMealPlan = LocalDBManager.Instance.GetDBSetting("FinalMealPlan")?.Value;
                                //var gptResponse = await AnaliesAIWithChatGPT($"Currently, my meal plan consists of the following:" +
                                //var changeQuery = $"Currently, my meal plan consists of the following:" +
                                //    //$"\r\n{rearrangeData}" +
                                //    $"\r\n{data}" +
                                //     $"\r\n\r\nRequest:" +
                                //     $"\r\nI want to change my meal plan with:" +
                                //     //$"\r\nI need to adjust the quantities or add ingredients within above given meals and macros" +
                                //     $"\r\n{rearrangeData}" +
                                //     //$"\r\nPlease recalculate exact and precise macros" +
                                //     $"\r\nNote: Please maintain the format and header as provided (no prose, no blank line, nothing else). " +
                                //     $"\r\nDo not introduce new meals, Adjustments should be made within the specified meal types.";
                                //var response = await GetChangeAfterGPT3(changeQuery);
                                if (!string.IsNullOrEmpty(rearrangeData))
                                {
                                    int startIndex = 0;
                                    if(!string.IsNullOrWhiteSpace(header))
                                     startIndex = ApprovedMeals.IndexOf(header);

                                    // Assuming there's a unique marker after the lunch section to find its end
                                    int endIndex = ApprovedMeals.IndexOf(",", startIndex);
                                    // Check if both the start and end of the lunch section were found
                                    if (startIndex != -1 && endIndex != -1)
                                    {
                                        // Remove the old lunch section, including up to the next "Separate"
                                        string toBeReplaced = ApprovedMeals.Substring(startIndex, endIndex - startIndex);
                                        ApprovedMeals = ApprovedMeals.Replace(toBeReplaced, rearrangeData + "\r\n\r\n");
                                    }

                                    //var reversedList = App.BotList.Reverse();
                                    if (App.BotList != null)
                                    {
                                        var botObj = App.BotList?.Where(a => a.Question.Contains(header)).FirstOrDefault();
                                        if (botObj != null)
                                        {
                                            int index = App.BotList.IndexOf(botObj);
                                            if (index >= 0 && index < App.BotList.Count)
                                            {
                                                botObj.Question = rearrangeData;
                                                botObj.IsAdjusting = true;
                                                App.BotList[index] = botObj;
                                            }

                                            if (index == 1)
                                                IsFirstMealChange = true;
                                            else if (index == 2)
                                                isSecondMealChange = true;
                                            else if (index == 3)
                                                isThirdMealChange = true;
                                            else if (index == 4)
                                                isForthMealChange = true;
                                            else if (index == 5)
                                                isFifthMealChange = true;
                                            else if (index == 6)
                                                isSixthMealChange = true;
                                        }
                                    }

                                    var array = App.MealTtypes.Split(',');
                                    //    var mealType = array?[0].TrimStart().ToLower(); // Ignore case
                                    //    string response = "";
                                    //switch (mealType)
                                    //{
                                    //    case "breakfast":
                                    //    return;
                                    //}

                                    GetResultFromGPT4(rearrangeData);
                                    //if (header.ToLower().Contains("breakfast"))
                                    //{
                                    //    IsFirstMealChange = true;
                                    //    string res = await GetMealByGPT4("breakfast", rearrangeData);
                                    //    var responseArray = ApprovedMeals.Split(',');
                                    //    var finalMealPlan = LocalDBManager.Instance.GetDBSetting("FinalMealPlan")?.Value;
                                    //    int startIndex1 = finalMealPlan.IndexOf(header);
                                    //    int endIndex1 = -1;

                                    //    try
                                    //    {
                                    //        endIndex1 = finalMealPlan.IndexOf("\nSeparate", startIndex1);
                                    //    }
                                    //    catch (Exception ex)
                                    //    {
                                    //    }

                                    //    // Check if both the start and end of the lunch section were found
                                    //    if (startIndex != -1 && endIndex != -1)
                                    //    {
                                    //        // Remove the old lunch section, including up to the next "Separate"
                                    //        string toBeReplaced1 = finalMealPlan.Substring(startIndex1, endIndex1 - startIndex1);
                                    //        finalMealPlan = finalMealPlan.Replace(toBeReplaced1, res + "\r\n\r\n");
                                    //    }
                                    //    else
                                    //    {

                                    //    }
                                    //    var botObj1 = App.BotList?.Where(a => a.Question.Contains(header)).FirstOrDefault();

                                    //    int index = App.BotList.IndexOf(botObj1);

                                    //    botObj1.Question = res;
                                    //    botObj1.IsAdjusting = false;
                                    //    App.BotList[index] = botObj1;
                                    //    LocalDBManager.Instance.SetDBSetting("FinalMealPlan", finalMealPlan);
                                    //    //AddAnswer(messageFromPopup);
                                    //    string[] sections = finalMealPlan.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                                    //    sections = sections.Where(section => !string.IsNullOrWhiteSpace(section)).ToArray();
                                    //    if (array.Length == sections.Length)
                                    //    {
                                    //        await UpdateTotalMacrosByGPT4(finalMealPlan);
                                    //    }
                                    //}

                                    //MessagingCenter.Send<GeneralMessage>(new GeneralMessage() { GeneralText = "Loaded" }, "ChangesToMealPlan");
                                }
                                //App.IsMealReponseLoaded = true;
                            }
                            catch (Exception ex)
                            {
                                //App.IsMealReponseLoaded = true;
                                RollbarLocator.RollbarInstance.Error(ex);
                            }
                        }
                    });
                }
                catch (Exception ex)
                {
                    RollbarLocator.RollbarInstance.Error(ex);
                }
                return;
            }
            else
            {
                DisplayAlert("Error", "MealGeneral Popup Page is null", "ok");
            }
        }
        catch (Exception ex)
        {

        }
    }

    private async void RegenerateGPT4ResponseForEachBubble(string data)
    {
        var count1 = App.BotList.Count;

        var header = "";
        var ToSearch = "";
        data = data.Replace("...", "");
        if (data.StartsWith("Protein Shake"))
        {
            data = data.Replace("Protein Shake", "Protein shake:");
            header = "Protein shake:";
            ToSearch = "Protein Shake";
        }
        else
            header = await GetHeaderForReceipe(data);
        try
        {
            MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
        }
        catch (Exception ex)
        {
        }
        string[] lines = data.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
        string firstLine = "";
        string MealType = "";
        string firstTwoLines = "";
        string caloriesLine = "";
        string remainingLines = "";
        // Check if there are at least two lines
        if (lines.Length >= 2)
        {
            firstLine = string.Join(Environment.NewLine, lines, 0, 1);
            firstTwoLines = string.Join(Environment.NewLine, lines, 0, 2);
            caloriesLine = string.Join(Environment.NewLine, lines, 1, 1);
            remainingLines = string.Join(Environment.NewLine, lines, 2, lines.Length - 2);
            try
            {
                var meal = firstLine.Split(':');
                MealType = meal?[0];
            }
            catch (Exception ex)
            {

            }
        }
        else
        {
        }
        var chatPopUp = new MealGeneralPopup();
        chatPopUp.SetPopupTitle("Edit meal", GeneralPopupEnum.ChangeMealPlan, "", remainingLines);
        if (chatPopUp != null)
        {
            await PopupNavigation.Instance.PushAsync(chatPopUp);
            var tcs = new TaskCompletionSource<string>();
            try
            {
                MessagingCenter.Subscribe<Message.GeneralMessage>(this, "GeneralMessage", async (obj1) =>
                {
                    //App.isChangePopupSubscribed = true;
                    if (obj1.IsCanceled)
                    {
                        //App.BotList.Remove(App.BotList.Last());
                        return;
                    }
                    NextArrow.IsVisible = false;
                    PreviousArrow.IsVisible = false;
                    //App.IsMealPlanLoading = true;
                    if (!string.IsNullOrEmpty(obj1.GeneralText))
                        tcs.TrySetResult(obj1.GeneralText);

                    string messageFromPopup = await tcs.Task;
                    if (!string.IsNullOrEmpty(messageFromPopup) || !string.IsNullOrWhiteSpace(messageFromPopup))
                    {
                        LocalDBManager.Instance.SetDBSetting("AllGroceries", "");
                        try
                        {
                            string newHeader = "";
                            newHeader = header;
                            newHeader = newHeader.ToLower();
                            if (newHeader.StartsWith("protein shake"))
                            {
                                newHeader = "protein shake";
                            }
                            
                            newHeader = newHeader.Trim();
                            newHeader = newHeader.ToLower();
                            string keyName = "Day" + currentDay.ToString() + "_" + newHeader + "_Recipe";

                            LocalDBManager.Instance.SetDBSetting(keyName, "");


                            string Isprotein = !string.IsNullOrEmpty(ToSearch) ? "Remember protein powder don't have carbs and fat." : "";
                            App.IsMealReponseLoaded = false;
                            var rearrangeData = firstTwoLines + "\r\n" + messageFromPopup;
                            var reversedList1 = App.BotList.Reverse();
                            var count = App.BotList.Count;
                            var existingMeal = "";
                            if (App.BotList != null)
                            {
                                var botObj1 = reversedList1?.Where(a => a.Question.Contains(header)).FirstOrDefault();
                                if (botObj1 != null)
                                {
                                    int index1 = App.BotList.IndexOf(botObj1);
                                    existingMeal = botObj1.Question;
                                    botObj1.IsAdjusting = true;
                                    if (index1 >= 0 && index1 < App.BotList.Count)
                                        App.BotList[index1] = botObj1;
                                }
                                else
                                {
                                    var h1 = header?.Split(":");
                                    var botObj2 = reversedList1?.Where(a => a.Question.Contains(h1?[0])).FirstOrDefault();
                                    if (botObj2 != null)
                                    {
                                        int index1 = App.BotList.IndexOf(botObj2);
                                        existingMeal = botObj2.Question;
                                        botObj2.IsAdjusting = true;
                                        if (index1 >= 0 && index1 < App.BotList.Count)
                                            App.BotList[index1] = botObj2;
                                    }
                                }

                            }

                            //var modalPage = new Views.GeneralPopup("Lamp.png", "", "", "");
                            //modalPage = new Views.GeneralPopup("Medal.png", $"Success!", $"Your changes are coming up.", "Continue", null, false, false, "false", "false", "false", "false", "true", "false", "false", false, true, "", "Change Result");
                            //await PopupNavigation.Instance.PushAsync(modalPage);

                            var finalMealPlan = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + currentDay)?.Value;
                            //var gptResponse = await AnaliesAIWithChatGPT($"Currently, my meal plan consists of the following:" +

                            var mealtypeQuery = (MealType?.ToLower() != "protein shake:" && MealType?.ToLower() != "protein shake") ? $"{MealType}: [ensure short title, maximum 3 words]" : "Protein shake:";
                            var headerQuery = (MealType?.ToLower() != "protein shake:" && MealType?.ToLower() != "protein shake") ? "Regenerate header suitable after changes of meal and don't show updated word." : "";

                            var changeQuery =
                                $"Original Meal Details:" +
                                    $"\r\n{existingMeal}" +
                                     $"\r\n\r\nChange Requested With Only Following Ingredients:" +
                                     $"\r\n{messageFromPopup}" +
                                     $"\r\n\r\nInstructions:" +
                                     $"\r\n1. **Ingredient Handling**:" +

                                 $"\r\n- If an ingredient is added without a specified quantity, use the smallest typical serving size for that ingredient and must show this size." +
                                 $"\r\n- If an ingredient is listed multiple times (singular or plural), sum their quantities and display the total." +
                                 //$"\r\n- Ensure if an ingredient is added multiple times (singular or plural), sum their quantities." +
                                 $"\r\n\r\n2. **Change Detection**:" +
                                 $"\r\n- Compare each ingredient in 'Change Requested' with 'Original Meal' ingredients:" +
                                 $"\r\n- Detect each change either its ingredient quantity or added/removed of any ingredient in 'Change Requested'." +
                                 $"\r\n- Remember I need to eat only 'Change Requested' ingredients and also don't add any ingredient with 0 unit" +
                                 $"\r\n- Ensure to recheck detect changes step twice to ensure accuracy." +
                                 $"\r\n- If no changes either in quantity or addition/removal, retain original macros: {caloriesLine}" +
                                 $"\r\n- If there are changes either in quantity or addition/removal, calculate macros for the changed ingredients only, then add/subtract from total and update the total macros accordingly without recalculating all for final." +

                                     $"\r\nIf after all calculations of 'final' result value goes negative then only in this case recalculate all macros for 'Change Requested'." +
                                      //$"\r\nOtherwise if any thing changed then please show and calculate values for changed ingredients and then ensure carefully to add or remove changed ingredients value respective to 'Current Meal Details:' values of {caloriesLine}." +
                                      //$"\r\n\r\nNext, we'll compute macros and calories. Add up total grams of protein, carbs, and fat of all ingredients using standard values from a reliable nutritional database. Then compute calories (1 g protein = 4 kcal, 1 g carbs = 4 kcal, 1 g fat = 9 kcal). If calories and macros don't match, compute again. Once they match, update values for meal. Round numbers." +
                                      //$"Then compute calories or kcal (1 g protein = 4 kcal, 1 g carbs = 4 kcal, 1 g fat = 9 kcal). " +
                                      //$"\r\n\r\nCalculate the nutritional content (calories, protein, carbs, fats) only for the changed ingredients using standard values from a reliable nutritional database." +
                                      //$"\r\n\r\nYou can try atleast 3 times to calculate macros but result should be accurate." +
                                      //$"i.e; \r\n1 egg (large) : 70 kcal" +
                                      //$"\r\nIf no change in current and updated meal then don't recalculate values but if anything is changed then only recalculate for changed item." +
                                      //$"Only changed ingredients should recalculate while other ingredients use existing values and also compare with 'Current Meal Details' nutrition values." +
                                      //$"\r\nNote: Please ensure and use precise values for calories/kcal and macros, also make sure it closer to 'Current Meal Details.'" +
                                      //$"\r\nMust Compare the updated meal's nutritional values with the 'Current Meal Details.' and only changed ingredients should recalculate otherwise same nutrition values." +
                                      $"\r\n\r\n3. **Macros and Calories Calculation**:" +
                                      $"\r\n- Use standard nutritional values from a reliable database." +
                                      $"\r\n- Calculate grams of protein, carbs, and fat for changed ingredients." +
                                      $"\r\n- Compute total calories and update in 'final' using the formula:" +
                                      $"\r\n    - Make sure Total kcal = (4 × protein in grams) + (4 × carbs in grams) + (9 × fat in grams)" +
                                      $"\r\n- Ensure calories match the macros, and round all values." +
                                      $"\r\n" +
                                     //$"Calculation Formula:\r\nShow calculation before 'Final:' at any cost. Total kcal: (4 * XX g (protein)) + (4 * XX g (carbs)) + (9 * XX g (fat)) = XXX kcal" +

                                     $"\r\n\r\n4. **Result Format**:" +
                                     $"\r\n- Display detailed calculations before 'Final:'." +
                                     $"\r\n- Round values of all macros and kcal to the nearest whole number." +
                                     $"\r\n- Ensure the header is suitable after meal changes [Short Title, max 3 words]." +
                                     $"\r\n- Ensure to use the changed meal details in the final output (no prose, no blank line, no * or any symbols, nothing else)." +
                                     $"\r\n- Display the sum of kcal just before final output = (4 × protein in grams) + (4 × carbs in grams) + (9 × fat in grams) and match with total kcal in result if exactly match then continue if not match then replace this kcal values in final output with this kcal value, and round all values to the nearest whole number." +

                                     $"" +
                                     //$"\r\n{headerQuery}" +
                                     //$"\r\nProvide the updated nutritional information in the specified format:" +
                                     //$"\r\nNote: Please maintain the format as provided (no prose, no blank line, nothing else). And give me result with precise, correct Calculations and then must use 'Final:' keyword with this format:" +
                                     //$"\r\n\r\n{firstTwoLines}" +
                                     $"\r\n\r\n(only one final section)'Final:' Format:" +
                                     $"\r\n{mealtypeQuery} [Short Title, max 3 words]" +
                                     $"\r\nXXX[Rounded] kcal, XX[Rounded] g protein, XX[Rounded] g carbs, XX[Rounded] g fat" +
                                     $"\r\n[Name of ingredient 1] (volume measure)" +
                                     $"\r\n[Name of ingredient 2] (volume measure)" +
                                     $"\r\n...";



                            //$"\r\n\r\nPlease use latest values for total kcal,total protein, total carbs, and total fat in final result and final result should not show in ranges." +
                            //$"\r\n" ;

                            //var changeQuery = $"I am updating the nutritional details for my meal and need precise calculations for the following ingredients." +
                            //$"\r\n" +
                            //$"\r\nCurrent Meal Details:" +
                            //    $"\r\n{data}" +
                            //    $"\r\n\r\nRequested Ingredients:" +
                            //    $"\r\n{messageFromPopup}" +
                            //    $"\r\n{Isprotein} " +
                            //    $"\r\n\r\nStrictly follow this specific format:" +
                            //    $"\r\n\r\n{firstLine}" +
                            //    $"\r\nXXX kcal, XX g protein, XX g carbs, XX g fat" +
                            //    $"\r\n[Name of ingredient 1] (volume measure)" +
                            //    $"\r\n[Name of ingredient 2] (volume measure)" +
                            //    $"\r\n[Name of ingredient n] (volume measure)" +
                            //    //$"\r\nXXX kcal, XX g protein, XX g carbs, XX g fat" +
                            //    //$"\r\n{messageFromPopup}" +
                            //    $"\r\nRecalculate the new caloric content and macros based on the requested ingredients." +
                            //    $"\r\nPlease use the following standard values for calculation:\r\ni.e; 1 Egg (large) : 70 kcal\r\n- Other ingredients: use standard values from a reliable nutritional database." +
                            //    $"\r\n\r\n" +
                            //    $"\r\nNote:" +
                            //    $"\r\nPlease Maintain the format at any cost (no prose, no blank line, nothing else). Replace 'XXX kcal, XX g protein, XX g carbs, XX g fat' with actual values after calculation of calories and macros. Repeat calculation until you get values for calories and macros." +
                            //    $"\r\nIf any ingredient don't have specified quantity, then please adjust their minimum quantities automatically and recalculate there macros." +
                            //    $"\r\nDon't show macros with ingredient.";
                            //$"\r\nTake current meal for comparison and ensure the recalculated values closely align with accepted standard nutritional data.";
                            //var changeQuery = $"Currently, my meal plan consists of the following:" +
                            //    //$"\r\n{rearrangeData}" +
                            //    $"\r\n{data}" +
                            //    $"\r\n\r\nRequest for Change:" +
                            //     $"\r\nAdjust the meal using the following ingredients:" +
                            //     //$"\r\nI need to adjust the quantities or add ingredients within above given meals and macros" +
                            //     $"\r\n{messageFromPopup}" +
                            //     $"\r\n{Isprotein} " +
                            //     $"\r\nRecalculate the new caloric content and macros based on the above ingredients. If change request ingredients not have correct quantity then please adjust it automatically.\r\nEnsure that the calculations are precise and present the updated information in the following format:\r\nPlease present the information in the following format and don't change meal header:\r\n{firstLine}\r\nXXX kcal, XX protein, XX g carbs, XX g fat\r\n- Ingredient\r\n- Ingredient. \r\nWithout adding any prose, blank lines, or new meals." +
                            //     //$"\r\nPlease calculate the new caloric content and macro distribution based on the above ingredients. Maintain the format and headers as shown, without adding any prose, blank lines, or new meals. Adjustments should only be made within the specified meal types." +
                            //     $"\r\nNote: Never include any note or details I just wanna show ingredients with quantity and don't use any symbol with macros";

                            //$"\r\nPlease recalculate exact and precise macros." +
                            //         $"\r\nNote: Please maintain the format and header as provided (no prose, no blank line, nothing else). " +
                            //         $"\r\nDo not introduce new meals, Adjustments should be made within the specified meal types.";
                            Console.WriteLine("Change Query =" + changeQuery);
                            var response = await GetChangeAfterGPT4(changeQuery);
                            Console.WriteLine("Change Result =" + response);
                            response = ConvertToFinalData(response, "");
                            while (response == "")
                            {
                                response = await GetChangeAfterGPT4(changeQuery);
                                response = ConvertToFinalData(response, "");
                            }
                            response = await RefineGPT4Data(response);
                            App.MealTtypes = LocalDBManager.Instance.GetDBSetting("MealTtypes")?.Value;
                            if (!string.IsNullOrEmpty(response))
                            {
                                int startIndex = finalMealPlan.IndexOf(header);
                                if (startIndex == -1)
                                {
                                    var meal = header.Split(':');
                                    startIndex = finalMealPlan.IndexOf(meal?[0]);
                                }
                                // Assuming there's a unique marker after the lunch section to find its end
                                int endIndex = finalMealPlan.IndexOf("\nSeparate", startIndex);

                            // Check if both the start and end of the lunch section were found
                            if (startIndex != -1 && endIndex != -1)
                            {
                                // Remove the old lunch section, including up to the next "Separate"
                                string toBeReplaced = finalMealPlan.Substring(startIndex, endIndex - startIndex);
                                finalMealPlan = finalMealPlan.Replace(toBeReplaced, response + "\r\n\r\n");
                            }
                            //AddAnswer(messageFromPopup);
                            var array = App.MealTtypes.Split(',');
                            var finalMealPlan1 = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + currentDay)?.Value;
                            string[] sections = finalMealPlan1.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                            sections = sections.Where(section => !string.IsNullOrWhiteSpace(section)).ToArray();
                            if (array.Length == sections.Length)
                            {
                                if (array.Length == sections.Length)
                                    await UpdateTotalMacrosByGPT4(finalMealPlan);
                                //await UpdateTotalMacrosByGPT4(finalMealPlan);
                                var surveyModel = new BotModel()
                                {
                                    StrengthImage = "survey_icon.png",
                                    Question = "Satisfied with meal plan?",
                                    Options = "MealPlan",
                                    Type = BotType.MealSurveyCard,
                                    SelectedSurveyOption = SatisfactionSurveyEnum.None
                                };
                                var reversedList = App.BotList.Reverse();
                                if (App.BotList != null && reversedList != null)
                                {
                                    var botObj = reversedList?.Where(a => a.Question.Contains(header)).FirstOrDefault();
                                    //var botObj = App.BotList?.FirstOrDefault(x => x.Question != null && (x.Question.Contains((!string.IsNullOrEmpty(ToSearch) ? ToSearch : header))));
                                    //var botObj = App.BotList.FirstOrDefault(x => x.Question.Contains((!string.IsNullOrEmpty(ToSearch) ? ToSearch : header)));
                                    // Find the index of the survey card within App.BotList
                                    if (botObj != null)
                                    {
                                        int index = App.BotList.IndexOf(botObj);
                                        if (index == -1)
                                        {
                                            var meal = header.Split(':');
                                            botObj = reversedList?.Where(a => a.Question.Contains(meal?[0])).FirstOrDefault();
                                            index = App.BotList.IndexOf(botObj);
                                        }
                                        botObj.Question = response;
                                        botObj.IsAdjusting = false;
                                        MainThread.BeginInvokeOnMainThread(() =>
                                        {
                                            if (index >= 0 && index < App.BotList.Count)
                                                App.BotList[index] = botObj;
                                        });
                                    }
                                    
                                }
                                var currentDayValue = currentDay;
                                LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDayValue, finalMealPlan);
                                Console.WriteLine("Shop list c= " + finalMealPlan);
                                string queryGroceryList = $"Generate a Shopping List" +
                                   $"\r\n\r\nConsolidate all ingredients from the recipes below into a single shopping list following these rules:" +
                                   $"\r\n\r\n1. Format Requirements:" +
                                   $"\r\n- List each ingredient with a hyphen (-)" +
                                   $"\r\n- Show only the final total quantity in parentheses" +
                                   $"\r\n- No calculations should be shown" +
                                   $"\r\n- Use this exact format:-Ingredient (total quantity unit) " +
                                   $"\r\n\r\nExample format:\r\n-Eggs (7 large)\r\n-Butter (2 tbsp)" +
                                   $"\r\n\r\n2. Measurement Rules:\r\n- Combine identical ingredients\r\n- Keep original units when possible\r\n- Never Round to whole numbers" +
                                   $"\r\n\r\nHeader must be 'Shopping List'" +
                                   $"\r\n\r\nRecipes:" +
                                   $"{finalMealPlan}" +
                                   $"\r\n\r\nNo additional text, explanations, or calculations should be included.";
                               // string queryGroceryList =
                               // $"Generate a consolidated grocery list with precise total quantities for the ingredients used in the recipes below. Ensure that quantities for the same ingredients across different recipes are accurately combined." +
                               //$"Pay special attention to ingredients that are mentioned multiple times, and ensure their quantities are added correctly. Thank you" +
                               //$"\r\n\r\n" +
                               //$"{finalMealPlan}" +
                               //$"\r\n\r\n" +
                               //$"Be careful the header should be 'Shopping List'\r\n" +
                               //$"Note: Please generate a single, comprehensive grocery list that includes exact total quantities for all ingredients listed across the meals specified. Put focus on Quantities of the same ingredients should be accurately added together." +
                               ////$"Note: Generate a single overall grocery list including exact quantities for all the ingredients listed above " +
                               //$"\r\nAnd be careful the header should be 'Shopping List'.";
                                var groceryList = await AnaliesAIWithChatGPT(queryGroceryList, 0, 3500, 0, 0, 0);

                                if (App.BotList != null && reversedList != null)
                                {
                                    var botObj1 = reversedList.FirstOrDefault(x => x.Question.Contains("Shopping List"));
                                    if (botObj1 != null)
                                    {
                                        // Find the index of the survey card within App.BotList
                                        int index1 = App.BotList.IndexOf(botObj1);

                                        botObj1.Question = groceryList.choices.FirstOrDefault()?.message.content;
                                        MainThread.BeginInvokeOnMainThread(() =>
                                        {
                                            if (index1 >= 0 && index1 < App.BotList.Count) // Ensure valid index
                                                App.BotList[index1] = botObj1;

                                            if (Device.RuntimePlatform == Device.Android)
                                            {
                                                lstChats.ItemsSource = null;
                                                lstChats.ItemsSource = App.BotList;
                                            }
                                        });
                                    }
                                }
                                //Device.BeginInvokeOnMainThread(() =>
                                //{
                                //    App.BotList[index1] = botObj1;
                                //    if (Device.RuntimePlatform == Device.Android)
                                //    {
                                //        lstChats.ItemsSource = null;
                                //        lstChats.ItemsSource = App.BotList;
                                //    }

                                //});
                                LocalDBManager.Instance.SetDBSetting($"FinalGroceryListDay" + currentDay, groceryList.choices.FirstOrDefault()?.message.content);

                                // Combine Grocery List
                                string AllGroceries = "";
                                for (int h = 1; h <= 7; h++)
                                {
                                    var grocery = LocalDBManager.Instance.GetDBSetting("FinalGroceryListDay" + h)?.Value;
                                    if (!string.IsNullOrEmpty(grocery))
                                    {
                                        AllGroceries += grocery;
                                        AllGroceries += "\n\n,\n\n";
                                    }
                                }
                                if (!string.IsNullOrEmpty(AllGroceries))
                                {
                                    //var query = $"Combine the following grocery lists, summing up exact quantities for each item:\n\n\n{AllGroceries}" +
                                    var query = $"Combine Shopping Lists" +
                                    $"\r\n\r\nRules for combination:" +
                                    $"\r\n\n\n1. Mathematical Precision:" +
                                    $"\r\n\r\n- Add quantities of identical ingredients" +
                                    $"\r\n\r\n2. Unit Standardization:" +
                                    $"\r\n\r\n- Keep measurements in their most practical unit" +
                                    $"\r\n\r\n- Standardize similar ingredients to the same unit" +
                                    $"\r\n\r\n- Never Round to whole numbers" +
                                    $"\r\n\r\n3. Format:" +
                                    $"\r\n\r\n-Ingredient (total quantity unit)" +
                                    $"\r\n\r\n4. Verification Steps:\r\n\r\n- Double-check all calculations\r\n- Ensure consistent units across lists\r\n- Verify no ingredients are missed" +
                                    $"\r\n\r\nShopping Lists:\r\n\r\n{AllGroceries}" +
                                        $"\n\nBe careful the header should be 'Shopping List' (no prose, no blank line, nothing else)";
                                    var gptResponse = await AnaliesAIWithChatGPT4o(query, 0, 2000, 0, 0, 0, "");
                                    string data = gptResponse.choices.FirstOrDefault()?.message.content;

                                        if (!string.IsNullOrEmpty(data))
                                        {
                                            LocalDBManager.Instance.SetDBSetting("AllGroceries", data);
                                        }
                                    }
                                    // Combine Grocery List

                                    if (App.BotList.Last().Type != BotType.MealSurveyCard)
                                    {
                                        App.BotList.Add(surveyModel);
                                        App.IsMealPlanChange = true;
                                    }
                                }
                                else
                                {
                                    var reversedList = App.BotList.Reverse();
                                    if (App.BotList != null && reversedList != null)
                                    {
                                        var botObj = reversedList?.Where(a => a.Question.Contains(header)).FirstOrDefault();
                                        if (botObj != null)
                                        {
                                            int index = App.BotList.IndexOf(botObj);
                                            if (index == -1)
                                            {
                                                var meal = header.Split(':');
                                                botObj = reversedList?.Where(a => a.Question.Contains(meal?[0])).FirstOrDefault();
                                                if(botObj != null)
                                                index = App.BotList.IndexOf(botObj);
                                            }
                                            botObj.Question = response;
                                            botObj.IsAdjusting = false;
                                            if (index >= 0  && index < App.BotList.Count)
                                            {
                                                App.BotList[index] = null;
                                                App.BotList[index] = botObj;
                                            }
                                        }
                                    }
                                    LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDay, finalMealPlan);
                                }


                                MessagingCenter.Send<GeneralMessage>(new GeneralMessage() { GeneralText = "Loaded" }, "ChangesToMealPlan");
                            }
                            NextArrow.IsVisible = true;
                            PreviousArrow.IsVisible = true;
                            App.IsMealPlanLoading = false;
                            App.IsMealReponseLoaded = true;
                        }
                        catch (Exception ex)
                        {
                            App.IsMealReponseLoaded = true;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
            }
            return;


        }
        else
        {
            DisplayAlert("Error", "The Page is not accessible at the moment. Try Again", "Ok");
        }

    }
    public  async void OnBeforeShow()
    {
        //base.OnBeforeShow();
        LoadSavedWeightFromServer();
        lstChats.ItemsSource = null;
        lstChats.ItemsSource = App.BotList;
        IsOnbeforeCalled = true;

        try
        {
            MessagingCenter.Subscribe<SubscriptionModel>(this, "SubscriptionPurchaseMessage", (obj) =>
            {
                if (obj == null)
                    return;
                UpdateSubscriptionData(obj);
            });

            MessagingCenter.Subscribe<SubscriptionModel>(this, "SubscriptionPurchaseIfNotExistMessage", (obj) =>
            {
                if (obj == null)
                    return;
                AddSubscriptionDataIfNotExist(obj);
            });
            MessagingCenter.Subscribe<string>(this, "ChangeEachBubblePlan", (obj) =>
            {
                if (obj == null)
                    return;
                var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                if (topPopup != null && topPopup.GetType() == typeof(MealGeneralPopup))
                {
                }
                else
                    RegenerateGPT4ResponseForEachBubble(obj);
            });
            MessagingCenter.Subscribe<string>(this, "ChangeEachBubblePlanFor3", (obj) =>
            {
                if (obj == null)
                    return;
                RegenerateGPT3ResponseForEachBubble(obj);
            });

            MessagingCenter.Subscribe<string>(this, "regenerateBubblePlan", async (obj) =>
            {
                if (obj == null)
                    return;

                var header = "";
                obj = obj.Replace("...", "");
                var ToSearch = "";
                if (obj.StartsWith("Protein Shake"))
                {
                    obj = obj.Replace("Protein Shake", "Protein shake:");
                    header = "Protein shake:";
                    ToSearch = "Protein Shake";
                }
                else
                    header = await GetHeaderForReceipe(obj);

                var existingQuestions = LocalDBManager.Instance.GetDBSetting("Plan")?.Value;
                var plan1 = JsonConvert.DeserializeObject<DmmMealPlan>(existingQuestions);

                string newHeader = "";
                newHeader = header;
                newHeader = newHeader.ToLower();
                if (newHeader.StartsWith("protein shake"))
                {
                    newHeader = "protein shake";
                }

                newHeader = newHeader.Trim();
                newHeader = newHeader.ToLower();
                string keyName = "Day" + currentDay.ToString() + "_" + newHeader + "_Recipe";
                LocalDBManager.Instance.SetDBSetting(keyName, "");

                //var newHeader = header.Trim();
                //newHeader = newHeader.ToLower();
                //string keyName = "Day" + currentDay.ToString() + "_"+ newHeader + "_Recipe";
                //LocalDBManager.Instance.SetDBSetting(keyName, "");

                NextArrow.IsVisible = false;
                PreviousArrow.IsVisible = false;

                var botObj1 = App.BotList?.Where(a => a.Question.Contains(header)).FirstOrDefault();
                if (botObj1 != null)
                {
                    int index1 = App.BotList.IndexOf(botObj1);
                    botObj1.IsAdjusting = true;
                    App.BotList[index1] = botObj1;
                }

                var prompt = await GetPromptforRegenerateMeal(plan1, obj);
                var gptResponse = await AnaliesAIWithChatGPT4o(prompt, 0, 2000, 0, 0, 0, "");
                string data = gptResponse.choices.FirstOrDefault()?.message.content;

                if (!string.IsNullOrEmpty(data))
                {
                    await MyRegeneratedMeal(data, header);
                }
            });
            MessagingCenter.Subscribe<string>(this, "changeRecipeBubble", async (obj) =>
            {
                try
                {
                    if (obj == null)
                        return;

                    if (App.IsMealPlanReceipeOpened)
                    {
                        return;
                    }
                    App.IsMealPlanReceipeOpened = true;
                    Task.Delay(300);
                    string header = "";
                    if (obj.StartsWith("Protein Shake"))
                    {
                        header = "Protein Shake";
                    }
                    else
                    {
                        header = await GetHeaderForReceipe(obj);
                        header = header.Split(':')[1];
                        header = header.TrimStart();
                    }
                    string[] lines = obj.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                    string remainingLines = "";
                    // Check if there are at least two lines
                    if (lines != null && lines.Length >= 2)
                    {
                        remainingLines = string.Join(Environment.NewLine, lines, 2, lines.Length - 2);
                    }

                    var newHeader = header.Trim();
                    newHeader = newHeader.ToLower();
                    string keyName = "Day" + currentDay.ToString() + "_"+ newHeader + "_Recipe";
                    //string keyName = "Day" + currentDay.ToString() + "_"+ header.Trim() + "_Recipe";

                    var body = LocalDBManager.Instance.GetDBSetting(keyName)?.Value;

                    if (!string.IsNullOrEmpty(body))
                    {
                        var modalPage1 = new Views.MealRecipePopup("cooking_recipe",  header, body, "Back to plan");
                        Application.Current.MainPage.ShowPopupAsync(modalPage1);
                    }
                    else
                    {
                        var modalPage = new Views.MealRecipePopup("cooking_recipe", header, "Loading...", "Back to plan");
                        Application.Current.MainPage.ShowPopupAsync(modalPage);
                        var data = await RegenerateGPT4RecipeBubble(header + "\n\r" + remainingLines, keyName);
                        if (!string.IsNullOrEmpty(data))
                        {
                            //if (PopupNavigation.Instance.PopupStack?.Count > 0 && PopupNavigation.Instance.PopupStack.Contains(modalPage))
                            //{
                                try
                                {
                                    modalPage.OnBefore(keyName);

                                }
                                catch (Exception ex)
                                {

                                }
                            //}
                        }
                    }
                    
                }
                catch (Exception ex)
                {

                }
            });

        }
        catch (Exception ex)
        {

        }

        try
        {
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     Message = AppResources.PleaseCheckInternetConnection,
                //     Title = AppResources.ConnectionError,
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Try again"
                // });
                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");
                return;
            }

            MessagingCenter.Unsubscribe<string>(this, "ChangePlan");
            MessagingCenter.Subscribe<string>(this, "ChangePlan", async (obj) =>
            {

                try
                {
                    MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
                }
                catch (Exception ex)
                {

                }
                //if (App.isChangePopupSubscribed)
                //{
                //    MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
                //    App.isChangePopupSubscribed = false;
                //}
                var chatPopUp = new MealGeneralPopup();
                chatPopUp.SetPopupTitle("Change meal plan", GeneralPopupEnum.ChangeMealPlan, "", "What changes would you like?");
                var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                if (topPopup != null && topPopup.GetType() == typeof(MealGeneralPopup))
                {
                    return;
                }
                if (chatPopUp != null)
                {
                    await PopupNavigation.Instance.PushAsync(chatPopUp);
                }
                else
                {
                    DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");
                    return;
                }    
                    var tcs = new TaskCompletionSource<string>();
                try
                {
                    MessagingCenter.Subscribe<Message.GeneralMessage>(this, "GeneralMessage", async (obj1) =>
                    {
                        //App.isChangePopupSubscribed = true;
                        if (obj1.IsCanceled)
                        {
                            //App.BotList.Remove(App.BotList.Last());
                            return;
                        }
                        if (!string.IsNullOrEmpty(obj1.GeneralText))
                            tcs.TrySetResult(obj1.GeneralText);

                        string messageFromPopup = await tcs.Task;
                        if (!string.IsNullOrEmpty(messageFromPopup) || !string.IsNullOrWhiteSpace(messageFromPopup))
                        {
                            NextArrow.IsVisible = false;
                            PreviousArrow.IsVisible = false;
                            LocalDBManager.Instance.SetDBSetting("AllGroceries", "");
                            try
                            {
                                App.BotList.Clear();
                                AddAnswer(messageFromPopup);
                                //App.IsMealReponseLoaded = false;

                                var modalPage = new Views.GeneralPopup("lamp.png", "", "", "");
                                modalPage = new Views.GeneralPopup("medal.png", $"Success!", $"Your changes are coming up.", "Continue", null, false, false, "false", "false", "false", "false", "true", "false", "false", false, true, "", "Change Result");
                                if (modalPage != null)
                                    await PopupNavigation.Instance.PushAsync(modalPage);
                                else
                                {
                                    DisplayAlert("Error", "The page is not accessible. Try again", "Ok");
                                    return;
                                }
                                var finalMealPlan = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + currentDay)?.Value;
                                //var gptResponse = await AnaliesAIWithChatGPT($"Currently, my meal plan consists of the following:" +
                                var changeQuery = $"Currently, my meal plan consists of the following:" +
                                    $"\r\n{finalMealPlan}" +
                                     $"\r\n\r\nRequest:" +
                                     $"\r\nI need to adjust the quantities or add ingredients within above given meals and macros" +
                                     $"\r\n{messageFromPopup}" +
                                     $"\r\nIf change any thing then please recalculate exact macros" +
                                     $"\r\nNote: Please maintain the format and header as provided (no prose, no blank line, never include any kind of note, nothing else). " +
                                     $"\r\nBefore each header add '\r\nSeparate\r\n' word and then breakline" +
                                     $"\r\nDo not introduce new meals, Adjustments should be made within the specified meal types." +
                                     $"\r\nDon't forget to add a blank line after every Meal.";
                                var response = await GetChangeAfterGPT4(changeQuery);

                                if (!string.IsNullOrEmpty(response))
                                {
                                    bool IsSurveryCardExist = false;
                                    var surveyModel = new BotModel()
                                    {
                                        StrengthImage = "survey_icon.png",
                                        Question = "Satisfied with meal plan?",
                                        Options = "MealPlan",
                                        Type = BotType.MealSurveyCard,
                                        SelectedSurveyOption = SatisfactionSurveyEnum.None
                                    };
                                    //if (App.BotList.Last() == surveyModel)
                                    //{
                                    //    IsSurveryCardExist = true;
                                    //    App.BotList.Remove(App.BotList.LastOrDefault());
                                    //}

                                    

                                    App.BotList.Clear();

                                    await GetTotalMacrosByGPT4(response);
                                    string[] sections = response.Split(new string[] { "Separate" }, StringSplitOptions.None);
                                    foreach (string section in sections)
                                    {
                                        if (!string.IsNullOrEmpty(section) && !string.IsNullOrWhiteSpace(section))
                                        {
                                            await AddMealPlan(section.Trim(), false, true, false, false, true, null);
                                        }
                                    }

                                    //await AddMealPlan(response, true, true,true,true);
                                    LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDay, response);
                                    string queryGroceryList = $"Generate a Shopping List" +
                       $"\r\n\r\nConsolidate all ingredients from the recipes below into a single shopping list following these rules:" +
                       $"\r\n\r\n1. Format Requirements:" +
                       $"\r\n- List each ingredient with a hyphen (-)" +
                       $"\r\n- Show only the final total quantity in parentheses" +
                       $"\r\n- No calculations should be shown" +
                       $"\r\n- Use this exact format:-Ingredient (total quantity unit) " +
                       $"\r\n\r\nExample format:\r\n-Eggs (7 large)\r\n-Butter (2 tbsp)" +
                       $"\r\n\r\n2. Measurement Rules:\r\n- Combine identical ingredients\r\n- Keep original units when possible\r\n- Never Round to whole numbers" +
                       $"\r\n\r\nHeader must be 'Shopping List'" +
                       $"\r\n\r\nRecipes:" +
                       $"{response}" +
                       $"\r\n\r\nNo additional text, explanations, or calculations should be included.";
                                   

                                    // string queryGroceryList =

                                    // $"Generate a consolidated grocery list with precise total quantities for the ingredients used in the recipes below. Ensure that quantities for the same ingredients across different recipes are accurately combined." +
                                    //$"\r\n\r\n" +
                                    //$"{response}" +
                                    //$"\r\n\r\n" +
                                    //$"Be careful the header should be 'Shopping List'\r\n" +
                                    //$"Note: Please generate a single, comprehensive grocery list that includes exact total quantities for all ingredients listed across the meals specified. Put focus on Quantities of the same ingredients should be accurately added together." +

                                    //$"\r\nAnd be careful the header should be 'Shopping List' (no prose, no blank line, nothing else).";
                                    var groceryList = await AnaliesAIWithChatGPT(queryGroceryList, 0, 3500, 0, 0, 0);
                                    await AddMealPlan(groceryList.choices.FirstOrDefault()?.message.content, true, false, false, false, false, null);
                                    LocalDBManager.Instance.SetDBSetting($"FinalGroceryListDay" + currentDay, groceryList.choices.FirstOrDefault()?.message.content);
                                    App.IsMealPlanChange = true;
                                    //Device.BeginInvokeOnMainThread(async () =>
                                    //{
                                    //    // Uncomment code please
                                    //    //lstChats.StopAutoScroll = true;
                                    //});
                                    //Device.BeginInvokeOnMainThread(() =>
                                    //{
                                    //    var resverseList = App.BotList.Reverse();
                                    //    var botObj = resverseList?.FirstOrDefault(x => x.Question != null && (x.Question.Contains("Final")));
                                    //    int index = App.BotList.IndexOf(botObj);
                                    //    lstChats.ScrollTo(App.BotList[index - 1], ScrollToPosition.MakeVisible, animate: false);
                                    //    lstChats.ScrollTo(App.BotList[index - 1], ScrollToPosition.Start, animate: false);

                                    //});
                                    // Combine Grocery List
                                    string AllGroceries = "";
                                    for (int h = 1; h <= 7; h++)
                                    {
                                        var grocery = LocalDBManager.Instance.GetDBSetting("FinalGroceryListDay" + h)?.Value;
                                        if (!string.IsNullOrEmpty(grocery))
                                        {
                                            AllGroceries += grocery;
                                            AllGroceries += "\n\n,\n\n";
                                        }
                                    }
                                    if (!string.IsNullOrEmpty(AllGroceries))
                                    {
                                        //var query = $"Combine the following grocery lists, summing up exact quantities for each item:\n\n\n{AllGroceries}" +
                                        var query = $"Combine Shopping Lists" +
                                    $"\r\n\r\nRules for combination:" +
                                    $"\r\n\n\n1. Mathematical Precision:" +
                                    $"\r\n\r\n- Add quantities of identical ingredients" +
                                    $"\r\n\r\n2. Unit Standardization:" +
                                    $"\r\n\r\n- Keep measurements in their most practical unit" +
                                    $"\r\n\r\n- Standardize similar ingredients to the same unit" +
                                    $"\r\n\r\n- Never Round to whole numbers" +
                                    $"\r\n\r\n3. Format:" +
                                    $"\r\n\r\n-Ingredient (total quantity unit)" +
                                    $"\r\n\r\n4. Verification Steps:\r\n\r\n- Double-check all calculations\r\n- Ensure consistent units across lists\r\n- Verify no ingredients are missed" +
                                    $"\r\n\r\nShopping Lists:\r\n\r\n{AllGroceries}" +
                                        $"\n\nBe careful the header should be 'Shopping List' (no prose, no blank line, nothing else)";
                                        var gptResponse = await AnaliesAIWithChatGPT4o(query, 0, 2000, 0, 0, 0, "");
                                        string data = gptResponse.choices.FirstOrDefault()?.message.content;

                                        if (!string.IsNullOrEmpty(data))
                                        {
                                            LocalDBManager.Instance.SetDBSetting("AllGroceries", data);
                                        }
                                    }
                                    // Combine Grocery List

                                    MessagingCenter.Send<GeneralMessage>(new GeneralMessage() { GeneralText = "Loaded" }, "ChangesToMealPlan");
                                    //Satisfaction Survey
                                    //if (IsSurveryCardExist)
                                        App.BotList.Add(surveyModel);
                                }
                                App.IsMealReponseLoaded = true;
                                
                            }
                            catch (Exception ex)
                            {
                                App.IsMealReponseLoaded = true;
                            }
                            finally
                            {
                                NextArrow.IsVisible = true;
                                PreviousArrow.IsVisible = true;
                            }
                        }
                    });
                }
                catch (Exception ex)
                {

                }
                finally
                {
                    NextArrow.IsVisible = true;
                    PreviousArrow.IsVisible = true;
                }
                return;
            });



            lastResultForGPT3 = "";
            try
            {
                this.ToolbarItems.Clear();
                var generalToolbarItem = new ToolbarItem("Reset plan", "menu", ResetPlanAction, ToolbarItemOrder.Primary, 0);
                this.ToolbarItems.Add(generalToolbarItem);
                App.BotList?.Clear();
            }
            catch (Exception ex)
            {

            }
            stackOptions.Children?.Clear();
            double planDay = 1;
            var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
            var MealPlan = LocalDBManager.Instance.GetDBSetting("FinalMealPlanDay1")?.Value;
            bool isMealPlanLoading = Preferences.Get($"IsMealPlanLoading{email}", false);
            if (string.IsNullOrEmpty(email))
            {
                isMealPlanLoading = false;
            }
            currentDay = 1;
            var planForExisting = Preferences.Get($"Plan{email}", "");
            if (!string.IsNullOrEmpty(planForExisting))
            {
                var plan1 = JsonConvert.DeserializeObject<DmmMealPlan>(planForExisting);
                planDay = plan1?.DaysOnPlan ?? 1;
                daysLabel.Text = "Day " + currentDay;
            }
            if (isMealPlanLoading == true && planDay == 1)
            {
                var macros = Preferences.Get($"MacrosDistribution{email}", "");

                var MealTtypes = Preferences.Get($"MealTtypes{email}", "");
                var FavoriteDiet = Preferences.Get($"FavoriteDiet{email}", "");
                LocalDBManager.Instance.SetDBSetting("FavoriteDiet", FavoriteDiet);
                LocalDBManager.Instance.SetDBSetting("Plan", planForExisting);
                LocalDBManager.Instance.SetDBSetting("MealTtypes", MealTtypes);
                LocalDBManager.Instance.SetDBSetting("MacrosDistribution", macros);


            }
            else if (string.IsNullOrEmpty(MealPlan))
            {
                MealPlan = Preferences.Get($"MealPlanDay1{email}", "");
                var MealPlan2 = Preferences.Get($"MealPlanDay2{email}", "");
                var MealPlan3 = Preferences.Get($"MealPlanDay3{email}", "");
                var MealPlan4 = Preferences.Get($"MealPlanDay4{email}", "");
                var MealPlan5 = Preferences.Get($"MealPlanDay5{email}", "");
                var MealPlan6 = Preferences.Get($"MealPlanDay6{email}", "");
                var MealPlan7 = Preferences.Get($"MealPlanDay7{email}", "");
                if (!string.IsNullOrEmpty(MealPlan))
                {
                    var grocery1 = Preferences.Get($"GroceryListDay1{email}", "");
                    var grocery2 = Preferences.Get($"GroceryListDay2{email}", "");
                    var grocery3 = Preferences.Get($"GroceryListDay3{email}", "");
                    var grocery4 = Preferences.Get($"GroceryListDay4{email}", "");
                    var grocery5 = Preferences.Get($"GroceryListDay5{email}", "");
                    var grocery6 = Preferences.Get($"GroceryListDay6{email}", "");
                    var grocery7 = Preferences.Get($"GroceryListDay7{email}", "");
                    var macros = Preferences.Get($"MacrosDistribution{email}", "");
                    var plan = Preferences.Get($"Plan{email}", "");
                    var MealTtypes = Preferences.Get($"MealTtypes{email}", "");
                    var FavoriteDiet = Preferences.Get($"FavoriteDiet{email}", "");
                    LocalDBManager.Instance.SetDBSetting("FavoriteDiet", FavoriteDiet);
                    LocalDBManager.Instance.SetDBSetting("Plan", plan);
                    LocalDBManager.Instance.SetDBSetting("MealTtypes", MealTtypes);
                    LocalDBManager.Instance.SetDBSetting("MacrosDistribution", macros);
                    LocalDBManager.Instance.SetDBSetting("FinalMealPlanDay1", MealPlan);
                    LocalDBManager.Instance.SetDBSetting("FinalGroceryListDay1", grocery1);

                    LocalDBManager.Instance.SetDBSetting("FinalMealPlanDay2", MealPlan2);
                    LocalDBManager.Instance.SetDBSetting("FinalGroceryListDay2", grocery2);

                    LocalDBManager.Instance.SetDBSetting("FinalMealPlanDay3", MealPlan3);
                    LocalDBManager.Instance.SetDBSetting("FinalGroceryListDay3", grocery3);
                
                    LocalDBManager.Instance.SetDBSetting("FinalMealPlanDay4", MealPlan4);
                    LocalDBManager.Instance.SetDBSetting("FinalGroceryListDay4", grocery4);
                
                    LocalDBManager.Instance.SetDBSetting("FinalMealPlanDay5", MealPlan5);
                    LocalDBManager.Instance.SetDBSetting("FinalGroceryListDay5", grocery5);
                
                    LocalDBManager.Instance.SetDBSetting("FinalMealPlanDay6", MealPlan6);
                    LocalDBManager.Instance.SetDBSetting("FinalGroceryListDay6", grocery6);
                
                    LocalDBManager.Instance.SetDBSetting("FinalMealPlanDay7", MealPlan7);
                    LocalDBManager.Instance.SetDBSetting("FinalGroceryListDay7", grocery7);
                }
            }


            try
            {
                var cal = LocalDBManager.Instance.GetDBSetting("LastMealCaloriesValue");

                if (cal != null)
                {
                    currentCalories = Convert.ToDecimal(cal?.Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
                }
                var target = LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value;
                if (!string.IsNullOrEmpty(target))
                {
                    currentTargetIntake = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("TargetIntake").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
                }

                if (currentCalories > currentTargetIntake)
                {
                    calDifference = currentCalories - currentTargetIntake;
                }
                else
                {
                    calDifference = currentTargetIntake - currentCalories;
                }
            }
            catch (Exception ex)
            {

            }

            if (App.IsResetPlan == true)
            {
                App.minPro = 0;
                App.maxPro = 0;
                App.minCarb = 0;
                App.maxCarb = 0;
                App.minFat = 0;
                App.maxFat = 0;
                App.MealTtypes = "";

                App.BreakfastMinCalories = 0;
                App.BreakfastMaxCalories = 0;
                App.BreakfastMinProteinGrams = 0;
                App.BreakfastMaxProteinGrams = 0;
                App.BreakfastMinCarbsGrams = 0;
                App.BreakfastMaxCarbsGrams = 0;
                App.BreakfastMinFatGrams = 0;
                App.BreakfastMaxFatGrams = 0;
                
                App.LunchMinCalories = 0;
                App.LunchMaxCalories = 0;
                App.LunchMinProteinGrams = 0;
                App.LunchMaxProteinGrams = 0;
                App.LunchMinCarbsGrams = 0;
                App.LunchMaxCarbsGrams = 0;
                App.LunchMinFatGrams = 0;
                App.LunchMaxFatGrams = 0;
                
                App.DinnerMinCalories = 0;
                App.DinnerMaxCalories = 0;
                App.DinnerMinProteinGrams = 0;
                App.DinnerMaxProteinGrams = 0;
                App.DinnerMinCarbsGrams = 0;
                App.DinnerMaxCarbsGrams = 0;
                App.DinnerMinFatGrams = 0;
                App.DinnerMaxFatGrams = 0;
                
                App.ProteinShakeCalories = 0;
                App.ProteinShakeProteinGrams = 30;
                App.ProteinShakeCarbsGrams = 12;
                App.ProteinShakeFatGrams = 4;
                
                App.Snack1MinCalories = 0;
                App.Snack1MaxCalories = 0;
                App.Snack1MinProteinGrams = 0;
                App.Snack1MaxProteinGrams = 0;
                App.Snack1MinCarbsGrams = 0;
                App.Snack1MaxCarbsGrams = 0;
                App.Snack1MinFatGrams = 0;
                App.Snack1MaxFatGrams = 0;
                
                App.Snack2MinCalories = 0;
                App.Snack2MaxCalories = 0;
                App.Snack2MinProteinGrams = 0;
                App.Snack2MaxProteinGrams = 0;
                App.Snack2MinCarbsGrams = 0;
                App.Snack2MaxCarbsGrams = 0;
                App.Snack2MinFatGrams = 0;
                App.Snack2MaxFatGrams = 0;
                App.plan = new DmmMealPlan();
                App.VegetarianEats = "";
                App.IsAnyAllergies = false;
                App.AllergyText = "";
                App.CountryText = "";
                App.FavouriteFood = "";


                isMealPlanLoading = false;
                App.IsMealPlanLoading = false;
                LocalDBManager.Instance.SetDBSetting("Macros", "");
                currentDay = 1;
                daysLabel.Text = "Day " + currentDay;
                GenerateBtnsStack.IsVisible = false;
                LocalDBManager.Instance.SetDBSetting("Plan", "");
                LocalDBManager.Instance.SetDBSetting("LastCaloriesValue", "");
                LocalDBManager.Instance.SetDBSetting("LastMealCaloriesValue", "");
                for (var i = 1; i <= 7; i++)
                {
                    LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + i, "");
                    LocalDBManager.Instance.SetDBSetting($"FinalGroceryListDay" + i, "");
                    Preferences.Set($"MealPlanDay{i}{email}", "");
                    Preferences.Set($"GroceryListDay{i}{email}", "");
                }

                try
                {
                    // Get all keys from the database
                    var allKeys = LocalDBManager.Instance.GetAllDBSettings("Day", "_Recipe");
                    // Delete each matching key
                    foreach (var key in allKeys)
                    {
                        LocalDBManager.Instance.DeleteDBSetting(key?.Key);
                    }
                }
                catch (Exception ex)
                {
                }
                Preferences.Set($"MacrosDistribution{email}", "");
                Preferences.Set($"Plan{email}", "");
                Preferences.Set($"MealTtypes{email}", "");
                Preferences.Set($"FavoriteDiet{email}", "");
                Preferences.Set($"IsMealPlanLoading{email}", false);
                App.IsMealPlanLoading = false;
                daysStack.IsVisible = false;
                App.IsResetPlan = false;
                resetClicked = true;
                App.BotList?.Clear();
                //stackOptions.Children.Clear();
                //OnBeforeShow();
                MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
                MessagingCenter.Subscribe<Message.GeneralMessage>(this, "GeneralMessage", (obj) =>
                {
                    IsBodyweightPopup = false;
                    HandleGeneralMessage(obj);

                });
                IsOnbeforeCalled = false;

                try
                {
                    if (_mainPage != null)
                    {
                        _mainPage.BtnMealPlan.Text = "Get MEAL PLAN";
                        _mainPage.BtnMealPlan2.Text = "Get MEAL PLAN";
                        //_mainPage.BtnUpdateMealPlan.Text = "Get MEAL PLAN";
                        _mainPage.MealPlanInsideCaloriesAdjustment_.Text = "Get MEAL PLAN";
                        _mainPage._isMealPlanExist = true;
                    }
                }
                catch (Exception ex)
                {

                }
            }
            else if (currentTargetIntake != currentCalories && calDifference >= 50 && !string.IsNullOrEmpty(MealPlan) && App.minPro != 0)
            {
                //try
                //{
                //    if (Device.RuntimePlatform == Device.iOS)
                //        await Task.Delay(1000);
                //}
                //catch (Exception ex)
                //{

                //}
                ReviseMealPlan();
            }
            else if (isMealPlanLoading == true && planDay == 1)
            {
                try
                {
                    if (Device.RuntimePlatform == Device.iOS)
                        await Task.Delay(1000);
                }
                catch (Exception ex)
                {

                }
                SyncWithServer();
            }
            
            else if (!string.IsNullOrEmpty(MealPlan))
            {
                daysStack.IsVisible = true;
                currentDay = 1;
                GenerateBtnsStack.IsVisible = false;
                daysLabel.Text = "Day " + currentDay;
                //FabImage.IsVisible = false;
                MainGrid.IsVisible = true;
                //mainScroll.IsVisible = false;
                App.IsResetPlan = false;
                await GetTotalMacrosByGPT4(MealPlan);
                string[] sections = MealPlan.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string section in sections)
                {
                    if (!string.IsNullOrEmpty(section) && !string.IsNullOrWhiteSpace(section))
                    {
                        //await AddMealPlan(section.Trim(), false, true, false, false, true);
                        await AddMealPlan(section.Trim(), false, true, false, false, true, false);
                    }
                }
                //await AddMealPlan(MealPlan, false, true,false);
                var grocery = LocalDBManager.Instance.GetDBSetting("FinalGroceryListDay1")?.Value;
                NextArrow.IsVisible = true;
                PreviousArrow.IsVisible = true;
                App.IsMealPlanLoading = false;
                if (!string.IsNullOrEmpty(grocery))
                {
                    await AddMealPlan(grocery, false, true, false, false, false, null);

                    // Remove Satisfaction Survey
                    var surveyModel = new BotModel()
                    {
                        StrengthImage = "survey_icon.png",
                        Question = "Satisfied with meal plan?",
                        Options = "MealPlan",
                        Type = BotType.MealSurveyCard,
                        SelectedSurveyOption = SatisfactionSurveyEnum.None
                    };
                    App.BotList.Add(surveyModel);

                }


            }
            else
            {
                currentDay = 1;
                daysStack.IsVisible = false;
                GenerateBtnsStack.IsVisible = false;
                App.IsResetPlan = false;
                //FabImage.IsVisible = false;
                if (!App.IsMealPlan)
                {
                    //mainScroll.IsVisible = false;
                    //MainGrid.IsVisible = false;
                    //mainScroll.IsVisible = true;
                    //var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    //var modalPage = new Views.GeneralPopup("Lists.png", "New! Meal plans—limited-time discount", "Get in shape faster with meal plans that update on autopilot. Discounted for a limited time to celebrate.", "Got it", null, false, false, "false", "false", "false");
                    //modalPage.Disappearing += (sender2, e2) =>
                    //{
                    //    waitHandle.Set();
                    //};
                    ////modalPage.OkButtonPress += ModalPage_OkButtonPress;
                    //await PopupNavigation.Instance.PushAsync(modalPage);

                    //await Task.Run(() => waitHandle.WaitOne());

                    //return;
                }

                CheckHeight();
            }
        }
        catch (Exception ex)
        {

        }
        finally
        {
            MainGrid.IsVisible = true;
        }
    }


    private async Task<string> GetPromptforRegenerateMeal(DmmMealPlan existingQuestions, string plan)
    {
        string countryDetails = $"Please include foods that easily found in country: {existingQuestions.Country}";

        StringBuilder requestAI = new StringBuilder();
        requestAI.AppendLine("This is my meal plan ");
        requestAI.AppendLine($"{plan} ");
        requestAI.AppendLine("I want to regenerate this meal plan with exact kcal, protein, fats and carbs and also give me other foods for this and you can change the quantity of food and the use other foods as well and you should be change more than one food. also return the kcal, protein, carbs and fat  with same format above ");
        requestAI.AppendLine("Meal plan follow these conditions ");
        requestAI.AppendLine("must follow the exact format as the above plan ");
        requestAI.AppendLine($"You are a world-class diet coach. I am your client. " +
          $"Please use standard values from a reliable nutritional database for ingredients." +
          $"{(!string.IsNullOrEmpty(existingQuestions.Country) ? countryDetails : "")}");


        requestAI.AppendLine($"\r\n- Allergies to avoid: " + (existingQuestions.IsAnyAllergies ? '[' + existingQuestions.Allergies + ']' : "[none]"));
        requestAI.AppendLine($"\r\n- Foods to avoid: " + (existingQuestions.IsAnyFoodYouDontLike ? '[' + existingQuestions.AllFoodsYouDontLikes + ']' : "[none]"));
        requestAI.AppendLine($"- For protein,Be careful I only need [ {existingQuestions.VegetarianOptions} ]" +
            $"And exclude following foods: {unselectedSource}" +
            $"\r\n**Note: Please make sure output nutrition must exactly match the given targets. Check twice for accuracy and always use reliable nutritional database for ingredients (no prose, no blank line, nothing else).**" +
             $"\r\n\r\n**Macros and Calories Calculation**:" +
 $"\r\n    - Make sure Total kcal = (4 × protein in grams) + (4 × carbs in grams) + (9 × fat in grams)" +
 $"\r\n- Ensure calories match the macros, and round all values." +
 $"\r\n" +
            $"\r\n\r\nFormat your response exactly as follow (no prose, no blank line, nothing else):" +
            //$"\r\n\r\nBreakfast: [ensure short title, maximum 3 words]\r\nKcal, kcal, carbs, fat\r\n[Name of ingredient 1] (volume measure)" +
            //$"\r\n[Name of ingredient 2] (volume measure)\r\n..." +
            $"\r\n[Stop here]");

        return requestAI.ToString();
    }

    private async Task MyRegeneratedMeal(string data, string previousHeader)
    {
        var count1 = App.BotList.Count;
        string newHeader = "";
        var ToSearch = "";
        data = data.Replace("...", "");
        if (data.StartsWith("Protein Shake"))
        {
            data = data.Replace("Protein Shake", "Protein shake:");
            newHeader = "Protein shake:";
            ToSearch = "Protein Shake";
        }
        else
            newHeader = await GetHeaderForReceipe(data);

        string[] lines = data.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
        string firstLine = "";
        string MealType = "";
        string firstTwoLines = "";
        string caloriesLine = "";
        string remainingLines = "";
        // Check if there are at least two lines
        if (lines.Length >= 2)
        {
            firstLine = string.Join(Environment.NewLine, lines, 0, 1);
            firstTwoLines = string.Join(Environment.NewLine, lines, 0, 2);
            caloriesLine = string.Join(Environment.NewLine, lines, 1, 1);
            remainingLines = string.Join(Environment.NewLine, lines, 2, lines.Length - 2);
            try
            {
                var meal = firstLine.Split(':');
                MealType = meal?[0];
            }
            catch (Exception ex)
            {

            }
        }
        else
        {
        }


        LocalDBManager.Instance.SetDBSetting("AllGroceries", "");
        try
        {
            //string Isprotein = !string.IsNullOrEmpty(ToSearch) ? "Remember protein powder don't have carbs and fat." : "";
            App.IsMealReponseLoaded = false;
            //var rearrangeData = firstTwoLines + "\r\n" + messageFromPopup;
            var reversedList1 = App.BotList.Reverse();
            var count = App.BotList.Count;
            var existingMeal = "";

            var finalMealPlan = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + currentDay)?.Value;
            //var gptResponse = await AnaliesAIWithChatGPT($"Currently, my meal plan consists of the following:" +

            var mealtypeQuery = (MealType?.ToLower() != "protein shake:" && MealType?.ToLower() != "protein shake") ? $"{MealType}: [ensure short title, maximum 3 words]" : "Protein shake:";
            var headerQuery = (MealType?.ToLower() != "protein shake:" && MealType?.ToLower() != "protein shake") ? "Regenerate header suitable after changes of meal and don't show updated word." : "";

            App.MealTtypes = LocalDBManager.Instance.GetDBSetting("MealTtypes")?.Value;
            if (!string.IsNullOrEmpty(data))
            {
                int startIndex = finalMealPlan.IndexOf(previousHeader);
                if (startIndex == -1)
                {
                    var meal = previousHeader.Split(':');
                    startIndex = finalMealPlan.IndexOf(meal?[0]);
                }
                // Assuming there's a unique marker after the lunch section to find its end
                int endIndex = finalMealPlan.IndexOf("\nSeparate", startIndex);

                // Check if both the start and end of the lunch section were found
                if (startIndex != -1 && endIndex != -1)
                {
                    // Remove the old lunch section, including up to the next "Separate"
                    string toBeReplaced = finalMealPlan.Substring(startIndex, endIndex - startIndex);
                    finalMealPlan = finalMealPlan.Replace(toBeReplaced, data + "\r\n\r\n");
                }
                //AddAnswer(messageFromPopup);
                var array = App.MealTtypes.Split(',');
                var finalMealPlan1 = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + currentDay)?.Value;
                string[] sections = finalMealPlan1.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                sections = sections.Where(section => !string.IsNullOrWhiteSpace(section)).ToArray();
                if (array.Length == sections.Length)
                {
                    if (array.Length == sections.Length)
                        await UpdateTotalMacrosByGPT4(finalMealPlan);
                    //await UpdateTotalMacrosByGPT4(finalMealPlan);
                    var surveyModel = new BotModel()
                    {
                        StrengthImage = "survey_icon.png",
                        Question = "Satisfied with meal plan?",
                        Options = "MealPlan",
                        Type = BotType.MealSurveyCard,
                        SelectedSurveyOption = SatisfactionSurveyEnum.None
                    };
                    var reversedList = App.BotList.Reverse();
                    if (App.BotList != null)
                    {
                        var botObj = reversedList?.Where(a => a.Question.Contains(previousHeader)).FirstOrDefault();
                        // Find the index of the survey card within App.BotList
                        int index = App.BotList.IndexOf(botObj);
                        if (index == -1)
                        {
                            var meal = previousHeader.Split(':');
                            botObj = reversedList?.Where(a => a.Question.Contains(meal?[0])).FirstOrDefault();
                            index = App.BotList.IndexOf(botObj);
                        }
                        botObj.Question = data;
                        botObj.IsAdjusting = false;
                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            App.BotList[index] = botObj;
                        });

                    }
                    var currentDayValue = currentDay;
                    LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDayValue, finalMealPlan);
                    Console.WriteLine("Shop list c= " + finalMealPlan);
                    string queryGroceryList = $"Generate a Shopping List" +
                                   $"\r\n\r\nConsolidate all ingredients from the recipes below into a single shopping list following these rules:" +
                                   $"\r\n\r\n1. Format Requirements:" +
                                   $"\r\n- List each ingredient with a hyphen (-)" +
                                   $"\r\n- Show only the final total quantity in parentheses" +
                                   $"\r\n- No calculations should be shown" +
                                   $"\r\n- Use this exact format:-Ingredient (total quantity unit) " +
                                   $"\r\n\r\nExample format:\r\n-Eggs (7 large)\r\n-Butter (2 tbsp)" +
                                   $"\r\n\r\n2. Measurement Rules:\r\n- Combine identical ingredients\r\n- Keep original units when possible\r\n- Never Round to whole numbers" +
                                   $"\r\n\r\nHeader must be 'Shopping List'" +
                                   $"\r\n\r\nRecipes:" +
                                   $"{finalMealPlan}" +
                                   $"\r\n\r\nNo additional text, explanations, or calculations should be included.";

                    var groceryList = await AnaliesAIWithChatGPT(queryGroceryList, 0, 3500, 0, 0, 0);

                    var botObj1 = reversedList.FirstOrDefault(x => x.Question.Contains("Shopping List"));
                    // Find the index of the survey card within App.BotList
                    int index1 = App.BotList.IndexOf(botObj1);

                    botObj1.Question = groceryList.choices.FirstOrDefault()?.message.content;
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        App.BotList[index1] = botObj1;
                        if (Device.RuntimePlatform == Device.Android)
                        {
                            lstChats.ItemsSource = null;
                            lstChats.ItemsSource = App.BotList;
                        }
                    });

                    LocalDBManager.Instance.SetDBSetting($"FinalGroceryListDay" + currentDay, groceryList.choices.FirstOrDefault()?.message.content);

                    // Combine Grocery List
                    string AllGroceries = "";
                    for (int h = 1; h <= 7; h++)
                    {
                        var grocery = LocalDBManager.Instance.GetDBSetting("FinalGroceryListDay" + h)?.Value;
                        if (!string.IsNullOrEmpty(grocery))
                        {
                            AllGroceries += grocery;
                            AllGroceries += "\n\n,\n\n";
                        }
                    }
                    if (!string.IsNullOrEmpty(AllGroceries))
                    {
                        //var query = $"Combine the following grocery lists, summing up exact quantities for each item:\n\n\n{AllGroceries}" +
                        var query = $"Combine Shopping Lists" +
                                    $"\r\n\r\nRules for combination:" +
                                    $"\r\n\n\n1. Mathematical Precision:" +
                                    $"\r\n\r\n- Add quantities of identical ingredients" +
                                    $"\r\n\r\n2. Unit Standardization:" +
                                    $"\r\n\r\n- Keep measurements in their most practical unit" +
                                    $"\r\n\r\n- Standardize similar ingredients to the same unit" +
                                    $"\r\n\r\n- Never Round to whole numbers" +
                                    $"\r\n\r\n3. Format:" +
                                    $"\r\n\r\n-Ingredient (total quantity unit)" +
                                    $"\r\n\r\n4. Verification Steps:\r\n\r\n- Double-check all calculations\r\n- Ensure consistent units across lists\r\n- Verify no ingredients are missed" +
                                    $"\r\n\r\nShopping Lists:\r\n\r\n{AllGroceries}" +
                                        $"\n\nBe careful the header should be 'Shopping List' (no prose, no blank line, nothing else)";
                        var gptResponse = await AnaliesAIWithChatGPT4o(query, 0, 2000, 0, 0, 0, "");
                        string data1 = gptResponse.choices.FirstOrDefault()?.message.content;

                        if (!string.IsNullOrEmpty(data1))
                        {
                            LocalDBManager.Instance.SetDBSetting("AllGroceries", data1);
                        }
                    }
                    // Combine Grocery List

                    if (App.BotList.Last().Type != BotType.MealSurveyCard)
                    {
                        App.BotList.Add(surveyModel);
                        App.IsMealPlanChange = true;
                    }
                }
                else
                {
                    var reversedList = App.BotList.Reverse();
                    if (App.BotList != null)
                    {
                        var botObj = reversedList?.Where(a => a.Question.Contains(previousHeader)).FirstOrDefault();
                        int index = App.BotList.IndexOf(botObj);
                        if (index == -1)
                        {
                            var meal = previousHeader.Split(':');
                            botObj = reversedList?.Where(a => a.Question.Contains(meal?[0])).FirstOrDefault();
                            index = App.BotList.IndexOf(botObj);
                        }
                        botObj.Question = data;
                        botObj.IsAdjusting = false;
                        App.BotList[index] = null;
                        App.BotList[index] = botObj;
                    }
                    LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDay, finalMealPlan);
                }


                MessagingCenter.Send<GeneralMessage>(new GeneralMessage() { GeneralText = "Loaded" }, "ChangesToMealPlan");
            }
            NextArrow.IsVisible = true;
            PreviousArrow.IsVisible = true;
            App.IsMealPlanLoading = false;
            App.IsMealReponseLoaded = true;
        }
        catch (Exception ex)
        {
            App.IsMealReponseLoaded = true;
        }
    }
    private async Task<string> RegenerateGPT4RecipeBubble(string obj, string keyName)
    {
        try
        {
            StringBuilder query = new StringBuilder();
            query.Append("Provide step-by-step cooking instructions using the given ingredients and quantities.\r\n\r\nHere is my meal plan\r\n\r\n");
            query.Append(obj);
            //query.Append("\r\n (no prose, no blank line, nothing else, bit short, don't use numbering except use line break after each step)");
            query.Append("\r\n (no prose, no blank line, nothing else, bit short, return only instructions not gradients)");

            var gptResponse = await AnaliesAIWithChatGPT4o(query.ToString(), 0, 2000, 0, 0, 0, null);
            string data = gptResponse.choices.FirstOrDefault()?.message.content;
            if (data != null)
            {
                LocalDBManager.Instance.SetDBSetting(keyName, data);
                return data;
            }
            else
                return "";
        }
        catch (Exception ex)
        {
            return "";
        }

    }

    private async Task<string> GetChangeAfterGPT4(string changeQuery)
    {
        try
        {
            var gptResponse = await GetChatGPT4(changeQuery, 0, 3000, 0, 0, 0);
            string data = gptResponse.choices.FirstOrDefault()?.message.content;
            if (!string.IsNullOrEmpty(data) && data.Contains("..."))
            {
                data = await GetChangeAfterGPT4(changeQuery);
            }
            if(!string.IsNullOrEmpty(data) && data.Contains("```"))
            {
                data = data.Replace("```", "");
            }
            return data;
        }
        catch (Exception ex)
        {
            return await GetChangeAfterGPT4(changeQuery);
        }

    }
    private async Task<string> GetChangeAfterGPT3(string changeQuery)
    {
        try
        {
            var gptResponse = await AnaliesAIWithChatGPT(changeQuery, 0, 2500, 0, 0, 0);
            string data = gptResponse.choices.FirstOrDefault()?.message.content;
            return data;
        }
        catch (Exception ex)
        {
            return await GetChangeAfterGPT3(changeQuery);
        }

    }

    public async void ResetPlanAction()
    {
        try
        {
            
            SlideMenu.ShowMealPlanMenu();
            var isOpen = SlideMenu.ToggleMenu();
            SlideMenu.IsVisible =await isOpen;
            //((RightSideMasterPage)SlideMenu).ShowMealPlanMenu();
            //if (SlideMenu.IsShown)
            //{
            //    HideMenu();
            //}
            //else
            //{
            //    ShowMenu();
            //}

        }
        catch (Exception ex)
        {

        }
    }
    protected override async void OnAppearing()
    {
        base.OnAppearing();
        if (IsOnbeforeCalled == false && App.IsAppGoesBackground == false)
        {
            App.IsAppGoesBackground = false;
            OnBeforeShow();
            MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
            MessagingCenter.Subscribe<Message.GeneralMessage>(this, "GeneralMessage", (obj) =>
            {
                IsBodyweightPopup = false;
                HandleGeneralMessage(obj);

            });
            IsOnbeforeCalled = false;
            return;
        }
        App.IsAppGoesBackground = false;
        IsOnbeforeCalled = false;
        if (App.BotList?.Count == 0)
            StartSetup();
        //try
        //{
        //    var result = "";
        //    int lowReps = 0;
        //    int highreps = 0;
        //    try
        //    {
        //        lowReps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsminimum")?.Value);
        //        highreps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsmaximum")?.Value);
        //    }
        //    catch (Exception)
        //    {

        //    }
        //    var result1 = "";
        //    if (lowReps >= 5 && highreps <= 12)
        //    {
        //        result = "Build muscle and strength";
        //        result1 = "building muscle and strength";
        //    }
        //    else if (lowReps >= 8 && highreps <= 15)
        //    {
        //        result = "Build muscle and burn fat";
        //        result1 = "building muscle and burn fat";
        //    }
        //    else if (lowReps >= 5 && highreps <= 15)
        //    {
        //        result = "Build muscle";
        //        result1 = "building muscle";
        //    }
        //    else if (lowReps >= 12 && highreps <= 20)
        //    {
        //        result = "Burn fat";
        //        result1 = "burning fat";
        //    }
        //    else if (highreps >= 16)
        //    {
        //        result = "Build muscle and burn fat";
        //        result1 = "building muscle and burning fat";
        //    }
        //    else
        //    {
        //        if (LocalDBManager.Instance.GetDBSetting("Demoreprange") != null)
        //        {
        //            if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscle")
        //            {
        //                result = "Build muscle";
        //                result1 = "building muscle";
        //            }
        //            else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscleBurnFat")
        //            {
        //                result = "Build muscle and burn fat";
        //                result1 = "building muscle and burning fat";
        //            }
        //            else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "FatBurning")
        //            {
        //                result = "Burn fat";
        //                result1 = "burning fat";
        //            }
        //        }
        //    }

        //    LblWeightGoal = $"Track your weight to get custom tip to {result.ToLower()}.";


        //    if (App.IsOnboarding)
        //    {
        //        var text = "";
        //        try
        //        {
        //            if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscle")
        //            {
        //                text = "Build muscle";
        //            }
        //            else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscleBurnFat")
        //            {
        //                text = "Build muscle and burn fat";
        //            }
        //            else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "FatBurning")
        //            {
        //                text = "Burn fat";
        //            }
        //        }
        //        catch (Exception ex)
        //        {

        //        }


        //        App.IsOnboarding = false;
        //        //XX workouts / week
        //        var workoutname = "";
        //        var count = 3;
        //        int age = -1, xDays = 3;
        //        if (LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId") != null &&
        //                    LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel") != null &&
        //                    LocalDBManager.Instance.GetDBSetting("recommendedProgramId") != null &&
        //                    LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel") != null &&
        //                    LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout") != null)
        //        {

        //            workoutname = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel").Value;

        //            if (LocalDBManager.Instance.GetDBSetting("Age") != null && LocalDBManager.Instance.GetDBSetting("Age").Value != null)
        //            {
        //                age = int.Parse(LocalDBManager.Instance.GetDBSetting("Age").Value);
        //            }
        //            if (age != -1)
        //            {
        //                if (LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value.ToLower().Contains("push/pull/legs"))
        //                {
        //                    xDays = 6;
        //                }
        //                else if (LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel").Value.ToLower().Contains("split"))
        //                {
        //                    if (age < 30)
        //                        xDays = 5;
        //                    else if (age >= 30 && age <= 50)
        //                        xDays = 4;
        //                    else
        //                        xDays = 3;
        //                }
        //                else
        //                {
        //                    if (age < 30)
        //                        xDays = 4;
        //                    else if (age >= 30 && age <= 50)
        //                        xDays = 3;
        //                    else
        //                        xDays = 2;
        //                }
        //            }
        //        }

        //        MessagingCenter.Send<BodyweightUpdateMessage>(new BodyweightUpdateMessage(), "BodyweightUpdateMessage");
        //        //return;

        //    }
        //}
        //catch (Exception ex)
        //{

        //}
        //LoadSavedWeights();
        //if (!Config.IsMealEntered && !IsBodyweightPopup)
        //{
        //    if (!Config.IsMeal1)
        //    {
        //        var meal1 = new MealGeneralPopup();
        //        meal1.SetPopupTitle("Yesterday, what did you have for breakfast?", GeneralPopupEnum.Meal1, "", "List all foods");
        //        IsBodyweightPopup = true;
        //        PopupNavigation.Instance.PushAsync(meal1);
        //    }
        //    else if (!Config.IsMeal2)
        //    {
        //        var meal2 = new MealGeneralPopup();
        //        meal2.SetPopupTitle("Yesterday, what did you have for lunch?", GeneralPopupEnum.Meal2, "", "List all foods you remember");
        //        IsBodyweightPopup = true;
        //        PopupNavigation.Instance.PushAsync(meal2);
        //    }
        //    else if (!Config.IsMeal3)
        //    {
        //        var meal3 = new MealGeneralPopup();
        //        meal3.SetPopupTitle("Yesterday, what did you have for diner?", GeneralPopupEnum.Meal3, "", "List all foods you remember");
        //        IsBodyweightPopup = true;
        //        PopupNavigation.Instance.PushAsync(meal3);
        //    }

        //}
        //else if (App.IsDisplayPopup)
        //{
        //    App.IsDisplayPopup = false;
        //    var mealInfo = new MealInfoPopup();
        //    PopupNavigation.Instance.PushAsync(mealInfo);
        //}
        //else
        //{
        //    //var dt = new DateTime(Config.LastBodyWeightUpdate);
        //    //if ((DateTime.Now.Date - dt).TotalDays >= 7)
        //    //{
        //    //    //Update bodyweight here:
        //    //    var bodyWeight = new MealBodyweightPopup();
        //    //    bodyWeight.SetBodyWeightProperty(WeightType.UpdateBodyWeight);
        //    //    PopupNavigation.Instance.PushAsync(bodyWeight);
        //    //}
        //}

        MessagingCenter.Subscribe<Message.AddedMealInfoMessage>(this, "AddedMealInfoMessage", (obj) =>
        {
            IsBodyweightPopup = false;
            //App.IsFromNotification = false;

            SaveMealInfo(obj);
        });
        MessagingCenter.Subscribe<Message.BodyweightMessage>(this, "BodyweightMessage", (obj) =>
        {
            IsBodyweightPopup = false;
            BodyWeightMassUnitMessage(obj);
        });
        MessagingCenter.Subscribe<Message.GeneralMessage>(this, "GeneralMessage", (obj) =>
        {
            IsBodyweightPopup = false;
            HandleGeneralMessage(obj);

        });
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        try
        {

            if (App.IsAppGoesBackground == false)
            {
                BreakLoop = true;
                var (mealPlans, groceryLists, email, macrosDistribution, mealTypes, plan, FavouriteDiet, isMealPlanLoaded, macrosdata) = App.GetMealPlansAndGroceryLists();
                App.SetMealPlansAndGroceryLists(mealPlans, groceryLists, email, macrosDistribution, mealTypes, plan, FavouriteDiet, isMealPlanLoaded, macrosdata);
                App.BotList.Clear();
            }

            //if (PopupNavigation.PopupStack?.Count > 0)
            //{
            //    PopupNavigation.Instance.PopAllAsync();
            //}
        }
        catch (Exception ex)
        {


        }

        MessagingCenter.Unsubscribe<Message.AddedMealInfoMessage>(this, "AddedMealInfoMessage");
        MessagingCenter.Unsubscribe<Message.BodyweightMessage>(this, "BodyweightMessage");
        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "ChangeEachBubblePlan");
        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "regenerateBubblePlan");
        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "changeRecipeBubble");
        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "ChangeEachBubblePlanFor3");
        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "SubscriptionPurchaseIfNotExistMessage");
        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "SubscriptionPurchaseMessage");
    }

    protected override bool OnBackButtonPressed()
    {

        if (IsBodyweightPopup)
            return true;
        //Device.BeginInvokeOnMainThread(async () =>
        //{
        //    var result = await DisplayAlert("Exit", "Are you sure you want to exit", "Yes", "No");
        //    if (result)
        //    {
        //        var kill = DependencyService.Get<IKillAppService>();
        //        kill.ExitApp();
        //    }
        //});
        return false;

    }

    private async void CheckHeight()
    {
        App.BotList?.Clear();
        MainGrid.IsVisible = true;
        //mainScroll.IsVisible = false;
        if (new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value, CultureInfo.InvariantCulture), "kg").Lb > 150 && LocalDBManager.Instance.GetDBSetting("Height")?.Value == null)
        {
            CurrentLog.Instance.IsHeightPopup = true;
            AddQuestion("How tall are you?");
            await Task.Delay(100);
            await PopupNavigation.Instance.PushAsync(new UserHeightView());
            IsBodyweightPopup = true;
        }
        else
        {
            FavouriteDiet();
            App.FavouriteFood = "";
        }
    }

    private async void ModalPage_OkButtonPress(object sender, EventArgs e)
    {
        await Task.Delay(300);
       var ShowPopUp = await HelperClass.DisplayCustomPopup("","Are you sure you want to subscribe to the meal plan add-on?",
        "Subscribe",AppResources.Cancel);
            ShowPopUp.ActionSelected += async (sender,action) => {

                    if (action == Views.PopupAction.OK)
                    {
                        await DependencyService.Get<IDrMuscleSubcription>().BuyMealPlanAccess();
                    }
                    
                    
            };
           

        // ConfirmConfig subscribeConfig = new ConfirmConfig()
        // {

        //     Message = "Are you sure you want to subscribe to the meal plan add-on?",
        //     OkText = "Subscribe",
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     CancelText = AppResources.Cancel,
        // };

        // var x = await UserDialogs.Instance.ConfirmAsync(subscribeConfig);
        // if (x)
        //     await DependencyService.Get<IDrMuscleSubcription>().BuyMealPlanAccess();
        //else
        //    PagesFactory.PopAsync();
    }

    private async void HandleGeneralMessage(GeneralMessage general)
    {
        if (general.PopupEnum == Enums.GeneralPopupEnum.UserHeight)
        {
            IsBodyweightPopup = false;
            if (!general.IsCanceled)
            {
                await AddAnswer(general.GeneralText);
                //Ask meal Plan question right away
                ClearOptions();
                //FabImage.IsVisible = false;
                FavouriteDiet();
                App.FavouriteFood = "";
            }
            await Task.Delay(200);
        }

        if (general.PopupEnum == Enums.GeneralPopupEnum.Allergy)
        {
            App.AllergyText = general.GeneralText;
            if (!general.IsCanceled)
            {
                await AddAnswer(general.GeneralText);
            }
            await Task.Delay(200);
            ClearOptions();
            FoodsYouDontLikes();
            //SyncWithServer();
            //HowManyExercise();
        }
        if (general.PopupEnum == Enums.GeneralPopupEnum.FoodsYouDontLike)
        {
            App.FoodsYouDontLikeText = general.GeneralText;
            if (!general.IsCanceled)
            {
                await AddAnswer(general.GeneralText);
            }
            await Task.Delay(200);
            ClearOptions();
            RecommendedCalories();
        }
        if (general.PopupEnum == Enums.GeneralPopupEnum.Country)
        {
            App.CountryText = general.GeneralText;
            if (!general.IsCanceled)
            {
                await AddAnswer(general.GeneralText);

            }
            await Task.Delay(200);
            await ClearOptions();
            AnyAllergies();
        }
        if (general.PopupEnum == Enums.GeneralPopupEnum.Meal1)
        {

            await AddAnswer(general.GeneralText);
            var mealModel = new DmmMeal()
            {
                // Id = Guid.NewGuid(),
                MealInfo = $"Breakfast: {general.GeneralText}",
                //UserId = Guid.Parse(Config.UserId)
            };
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                await UserDialogs.Instance.AlertAsync("Please check your internet connection.", "Internet error");
                HandleGeneralMessage(general);
                return;
            }
            try
            {
                UserDialogs.Instance.ShowLoading("Updating...");
                var userMealModel = await DrMuscleRestClient.Instance.AddUserMealAsync(mealModel);
                UserDialogs.Instance.HideLoading();
                await Task.Delay(200);
            }
            catch
            {

            }
            Config.IsMeal1 = true;
            var meal2 = new MealGeneralPopup();
            meal2.SetPopupTitle("Yesterday, what did you have for lunch?", GeneralPopupEnum.Meal2, "", "List all foods you remember");
            IsBodyweightPopup = true;
            if (meal2 != null)
            {
                await PopupNavigation.Instance.PushAsync(meal2);
            }
            else
            {
                DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");
                return;
            }    
        }
        if (general.PopupEnum == Enums.GeneralPopupEnum.Meal2)
        {

            await AddAnswer(general.GeneralText);
            var mealModel = new DmmMeal()
            {
                // Id = Guid.NewGuid(),
                MealInfo = $"Lunch: {general.GeneralText}",
                // UserId = Guid.Parse(Config.UserId)
            };
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                await UserDialogs.Instance.AlertAsync("Please check your internet connection.", "Internet error");
                HandleGeneralMessage(general);
                return;
            }
            try
            {
                UserDialogs.Instance.ShowLoading("Updating...");
                var userMealModel = await DrMuscleRestClient.Instance.AddUserMealAsync(mealModel);
                UserDialogs.Instance.HideLoading();
                await Task.Delay(200);
            }
            catch(Exception ex)
            {
                RollbarLocator.RollbarInstance.Error(ex);
            }
            await Task.Delay(200);
            Config.IsMeal2 = true;
            var meal3 = new MealGeneralPopup();
            meal3.SetPopupTitle("Yesterday, what did you have for diner?", GeneralPopupEnum.Meal3, "", "List all foods you remember");
            IsBodyweightPopup = true;

            if (meal3 != null)
            {
                await PopupNavigation.Instance.PushAsync(meal3);
            }
            else
            {
                DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");
                return;
            }
        }
        if (general.PopupEnum == Enums.GeneralPopupEnum.Meal3)
        {

            await AddAnswer(general.GeneralText);
            var mealModel = new DmmMeal()
            {
                // Id = Guid.NewGuid(),
                MealInfo = $"Diner: {general.GeneralText}",
                // UserId = Guid.Parse(Config.UserId)
            };
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                await UserDialogs.Instance.AlertAsync("Please check your internet connection.", "Internet error");
                HandleGeneralMessage(general);
                return;
            }
            try
            {
                UserDialogs.Instance.ShowLoading("Updating...");
                var userMealModel = await DrMuscleRestClient.Instance.AddUserMealAsync(mealModel);
                UserDialogs.Instance.HideLoading();
                await Task.Delay(200);
            }
            catch(Exception ex)
            {
                RollbarLocator.RollbarInstance.Error(ex);
            }
            await Task.Delay(200);
            Config.IsMeal3 = true;
            Config.IsMealEntered = true;
            BtnAddMealPref_Clicked(new Button(), EventArgs.Empty);

        }
    }

    private async void StartSetup()
    {
        try
        {

            if (App.BotList?.Count == 0)
            {
                // App.BotList.Add(new BotModel() { Type = BotType.Chart });
            }
            await Task.Delay(600);
            GetLastMealPlanDate();
            ClearOptions();
            if (btnGetPlan == null)
            {
                // FabImage.IsVisible = true;

            }
            else
            {

            }
        }
        catch (Exception ex)
        {

        }
    }

    private async void GetLastMealPlanDate()
    {
        //if (Config.LastMealPlanOrderDate == null)
        //{
        //    var date = await _userService.GetLastMealPlanDateAsync(new UserModel() { Id = Guid.Parse(Config.UserId) });
        //    if (date != null)
        //        Config.LastMealPlanOrderDate = ((DateTime)date).ToString();
        //    var notification13 = new NotificationRequest
        //    {
        //        NotificationId = 110,
        //        Title = "No new meal plan?",
        //        Description = "Get new meal plan",
        //        ReturningData = "NewMealPlan",
        //        Android = { IconSmallName = { ResourceName = "eve_notification" } },
        //        Schedule = { NotifyTime = DateTime.Now.AddSeconds((((DateTime)date).AddDays(15) - DateTime.Now).TotalSeconds) }
        //    };

        //    NotificationCenter.Current.Show(notification13);
        //}
        //else
        //{
        //    try
        //    {

        //        if ((DateTime.Parse(Config.LastMealPlanOrderDate)).Date < DateTime.Now.AddDays(-15))
        //        {
        //            var notification13 = new NotificationRequest
        //            {
        //                NotificationId = 110,
        //                Title = "No new meal plan?",
        //                Description = "Get new meal plan",
        //                ReturningData = "NewMealPlan",
        //                Android = { IconSmallName = { ResourceName = "eve_notification" } },
        //                Schedule = { NotifyTime = DateTime.Now.AddSeconds((DateTime.Now.AddDays(15) - DateTime.Now).TotalSeconds) }
        //            };
        //        }
        //        else
        //        {
        //            var date = await _userService.GetLastMealPlanDateAsync(new UserModel() { Id = Guid.Parse(Config.UserId) });
        //            var notification13 = new NotificationRequest
        //            {
        //                NotificationId = 110,
        //                Title = "No new meal plan?",
        //                Description = "Get new meal plan",
        //                ReturningData = "NewMealPlan",
        //                Android = { IconSmallName = { ResourceName = "eve_notification" } },
        //                Schedule = { NotifyTime = DateTime.Now.AddSeconds((((DateTime)date).AddDays(15) - DateTime.Now).TotalSeconds) }
        //            };
        //        }

        //    }
        //    catch (Exception ex)
        //    {

        //    }
        //}
    }

    private async void BtnUpdateBodyWeight_Clicked(object sender, EventArgs e)
    {
        try
        {
            ((Button)sender).Clicked -= BtnUpdateBodyWeight_Clicked;
            var bodyWeight = new MealBodyweightPopup();
            bodyWeight.SetBodyWeightProperty(WeightType.UpdateBodyWeight);
            //_firebase.LogEvent("update_bodyweight", "");
            if (bodyWeight != null)
            {
                await PopupNavigation.Instance.PushAsync(bodyWeight);
                ((Button)sender).Clicked += BtnUpdateBodyWeight_Clicked;
            }
            else
            {
                DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");

            }
        }
        catch (Exception ex)
        {

        }
      
    }

    async void BtnAddMealPref_Clicked(object sender, EventArgs e)
    {
        try
        {
            //ActionStack.IsVisible = FabImage.IsVisible = false;

            ((Button)sender).Clicked -= BtnAddMealPref_Clicked;
            //_firebase.LogEvent("get_a_new_meal_plan", "");
            ClearOptions();
            FavouriteDiet();
            App.FavouriteFood = "";

            ((Button)sender).Clicked += BtnAddMealPref_Clicked;
        }
        catch(Exception ex)
        {

        }
    }

    async void BtnAddMealLog_Clicked(object sender, EventArgs e)
    {
        //ActionStack.IsVisible = FabImage.IsVisible = false;
        ((Button)sender).Clicked -= BtnAddMealLog_Clicked;
        //_firebase.LogEvent("log_a_meal", "");
        var mealInfo = new MealInfoPopup();
        if (mealInfo != null)
        {
            await PopupNavigation.Instance.PushAsync(mealInfo);
            ((Button)sender).Clicked += BtnAddMealLog_Clicked;
        }
        else
        {
            DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");
            return;
        }
        
    }

    private async void BodyWeightMassUnitMessage(BodyweightMessage bodyweight)
    {
        //ActionStack.IsVisible = FabImage.IsVisible = false;
        //if (App.IsFromNotification)
        //    return;
        //if (string.IsNullOrEmpty(Config.UserEmail))
        //    return;
        //else
        if (bodyweight.WeightType == WeightType.UpdateBodyWeight)
        {
            var unit = Config.MassUnit == "lb" ? "lbs" : "kg";
            await AddAnswer($"Body weight {bodyweight.BodyWeight} {unit}");
            await Task.Delay(300);
            Config.CurrentWeight = bodyweight.Weight.ToString();
            decimal goalWeight = 0;
            try
            {

                goalWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.Replace(",", "."), CultureInfo.InvariantCulture);
                Config.PredictedWeight = goalWeight.ToString();
            }
            catch (Exception ex)
            {

            }
            var weight = Config.MassUnit == "lb" ? Math.Round(new DrMuscleWebApiSharedModel.MultiUnityWeight(goalWeight, "kg").Lb, 2).ToString() : goalWeight.ToString();
            var targetUnit = Config.MassUnit == "lb" ? "lbs" : "kg";

           var alert = await HelperClass.DisplayCustomPopup($"Target body weight: {weight} {targetUnit}","Is this still correct?",
        "Yes, correct","No, update");
        var res = false;
            alert.ActionSelected += async (sender,action) => {

                    if (action == Views.PopupAction.OK)
                    {
                        res = true;
                    }
                
            };
         
            // ConfirmConfig p = new ConfirmConfig()
            // {

            //     Message = "Is this still correct?",
            //     Title = $"Target body weight: {weight} {targetUnit}",
            //     OkText = "Yes, correct",
            //     CancelText = "No, update"


            // };

            // var res = await UserDialogs.Instance.ConfirmAsync(p);
            if (!res)
            {
                var bodyWeight = new MealBodyweightPopup();
                bodyWeight.SetBodyWeightProperty(WeightType.UpdatePredictedWeight);
                if (bodyWeight != null)
                {
                    await PopupNavigation.Instance.PushAsync(bodyWeight);
                }
                else
                {
                    DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");
                    return;
                }

            }
            else
            {

                UserModel userModel = new UserModel()
                {
                    CurrentWeight = Convert.ToDouble(Config.CurrentWeight.Replace(",", "."), CultureInfo.InvariantCulture),
                    PredictedWeight = Convert.ToDouble(Config.PredictedWeight.Replace(",", "."), CultureInfo.InvariantCulture),
                    //Id = Guid.Parse(Config.UserId)
                };
                UserDialogs.Instance.ShowLoading("Processing...");
                //WeightChangedModel isUpdated = await _userService.UpdateBodyWeightAsync(userModel);
                UserDialogs.Instance.HideLoading();
                ClearOptions();
                App.BotList.Clear();
                //if (isUpdated == null)
                //    StartSetup();
                //else
                //    CheckBodyweightStatus(isUpdated);
                Config.LastBodyWeightUpdate = DateTime.Now.Date.Ticks;

                UpdateBodyweightNotification();



            }

        }
        else if (bodyweight.WeightType == WeightType.UpdatePredictedWeight)
        {
            var massunit = Convert.ToDouble(bodyweight.BodyWeight) > 1 ? Config.MassUnit == "lb" ? "lbs" : "kg" : Config.MassUnit;
            await AddAnswer($"Target body weight {bodyweight.BodyWeight} {massunit}");

            Config.PredictedWeight = bodyweight.Weight.ToString();


            UserModel userModel = new UserModel()
            {
                CurrentWeight = Convert.ToDouble(Config.CurrentWeight.Replace(",", "."), CultureInfo.InvariantCulture),
                PredictedWeight = Convert.ToDouble(Config.PredictedWeight.Replace(",", "."), CultureInfo.InvariantCulture),
                //Id = Guid.Parse(Config.UserId)
            };
            UserDialogs.Instance.ShowLoading("Processing...");
            //WeightChangedModel isUpdated = await _userService.UpdateBodyWeightAsync(userModel);
            UserDialogs.Instance.HideLoading();



            ClearOptions();
            App.BotList.Clear();
            //if (isUpdated == null)
            //    StartSetup();
            //else
            //    CheckBodyweightStatus(isUpdated);
            UpdateBodyweightNotification();
            Config.LastBodyWeightUpdate = DateTime.Now.Date.Ticks;

            StartSetup();

        }
    }

    private async void CheckBodyweightStatus(WeightChangedModel change)
    {
        try
        {

            var CurrentWeight = Convert.ToDouble(Config.CurrentWeight.Replace(",", "."), CultureInfo.InvariantCulture);
            var PredictedWeight = Convert.ToDouble(Config.PredictedWeight.Replace(",", "."), CultureInfo.InvariantCulture);
            var changeUnit = Config.MassUnit == "lb" ? Math.Round(new DrMuscleWebApiSharedModel.MultiUnityWeight((decimal)change.ChangedUnit, "kg").Lb, 2).ToString() : Math.Round(change.ChangedUnit, 2).ToString();
            var massunit = Convert.ToDouble(changeUnit) > 1 ? Config.MassUnit == "lb" ? "lbs" : "kg" : Config.MassUnit;
            var popupTitle = "";
            if (change.ChangedPercentage > 0)
                popupTitle = $"You have gained {changeUnit} {massunit} in {Math.Round(change.Days)} days";
            else if (change.ChangedPercentage == 0)
                popupTitle = $"Your weight has not changed";
            else
                popupTitle = $"You have lost {changeUnit} {massunit} in {Math.Round(change.Days)} days";
            bool isAccepted = false;

            var w = Config.MassUnit == "lb" ? Math.Round(new DrMuscleWebApiSharedModel.MultiUnityWeight((decimal)CurrentWeight, "kg").Lb) + " lbs" : (decimal)CurrentWeight + " kg";
            if (Config.LastMealPlanOrderDate != null)
            {
                DateTime dt = Convert.ToDateTime(Config.LastMealPlanOrderDate);
                if (dt.Date == DateTime.Now.Date)
                {
                    await DisplayAlert(popupTitle, "Nice work! You should continue on the same plan.", "Got it");
                    return;
                }
            }
            //Body loss
            if (CurrentWeight > PredictedWeight && change.ChangedPercentage > -0.1429)
            {
                isAccepted = await DisplayAlert(popupTitle, "You should get a plan with fewer calories.", "Get plan", "Skip");
            }
            else if (CurrentWeight > PredictedWeight && change.ChangedPercentage < -0.4287)
            {
                var isAcc = await DisplayAlert("Are you sure?", $"Seems you have lost a lot of weight. Are you sure {w} is correct?", $"{w} correct", "Cancel");
                if (isAcc)
                {
                    isAccepted = await DisplayAlert(popupTitle, "Weight updated. You should get a new plan.", "Get plan", "Skip");
                }
                else
                {
                    StartSetup();
                    BtnUpdateBodyWeight_Clicked(new Button(), EventArgs.Empty);
                    return;
                }

            }
            else if (CurrentWeight > PredictedWeight && change.ChangedPercentage < -0.1429)
            {
                await DisplayAlert(popupTitle, "Nice work! You should continue on the same plan.", "Got it");
            }



            //Body Gain

            else if (CurrentWeight <= PredictedWeight && change.ChangedPercentage < 0.0714)
            {
                isAccepted = await DisplayAlert(popupTitle, "You should get a plan with more calories.", "Get plan", "Skip");
            }

            else if (CurrentWeight <= PredictedWeight && change.ChangedPercentage > 0.1429)
            {
                var isAcc = await DisplayAlert("Are you sure?", $"Seems you have gain a lot of weight. Are you sure {w} is correct?", $"{w} correct", "Cancel");
                if (isAcc)
                {
                    isAccepted = await DisplayAlert(popupTitle, "Weight updated. You should get a new plan.", "Get plan", "Skip");
                }
                else
                {
                    App.BotList.Clear();
                    StartSetup();
                    BtnUpdateBodyWeight_Clicked(new Button(), EventArgs.Empty);
                    return;
                }

            }
            else if (CurrentWeight <= PredictedWeight && change.ChangedPercentage > 0.0714)
            {
                await DisplayAlert(popupTitle, "Nice work! You should continue on the same plan.", "Got it");
            }
            if (isAccepted)
            {
                App.FavouriteFood = "";
                FavouriteDiet();
            }
            else
            {
                App.BotList.Clear();
                StartSetup();
            }

        }
        catch (Exception ex)
        {

        }
    }

    public async void UpdateBodyweightNotification()
    {
        //NotificationCenter.Current.Cancel(105);
        //NotificationCenter.Current.Cancel(106);
        //NotificationCenter.Current.Cancel(107);
        //NotificationCenter.Current.Cancel(108);
        //var notification = new NotificationRequest
        //{
        //    NotificationId = 105,
        //    Title = "Eve Diet Coach",
        //    Description = "Time to update your body weight!",
        //    ReturningData = "Bodyweight",
        //    Android = { IconSmallName = { ResourceName = "eve_notification" } },
        //    Schedule = {
        //    NotifyTime = DateTime.Now.AddSeconds(24 * 7 * 60 * 60)
        //    }
        //};
        //NotificationCenter.Current.Show(notification);
        //var notification8 = new NotificationRequest
        //{
        //    NotificationId = 106,
        //    Title = "Eve Diet Coach",
        //    Description = "Time to update your body weight!",
        //    ReturningData = "Bodyweight",
        //    Android = { IconSmallName = { ResourceName = "eve_notification" } },
        //    Schedule = { NotifyTime = DateTime.Now.AddSeconds(24 * 8 * 60 * 60) }
        //};
        //NotificationCenter.Current.Show(notification8);
        //var notification10 = new NotificationRequest
        //{
        //    NotificationId = 107,
        //    Title = "Eve Diet Coach",
        //    Description = "Time to update your body weight!",
        //    ReturningData = "Bodyweight",
        //    Android = { IconSmallName = { ResourceName = "eve_notification" } },
        //    Schedule = { NotifyTime = DateTime.Now.AddSeconds(24 * 10 * 60 * 60) }
        //};
        //NotificationCenter.Current.Show(notification10);

        //var notification13 = new NotificationRequest
        //{
        //    NotificationId = 108,
        //    Title = "Eve Diet Coach",
        //    Description = "Time to update your body weight!",
        //    ReturningData = "Bodyweight",
        //    Android = { IconSmallName = { ResourceName = "eve_notification" } },
        //    Schedule = { NotifyTime = DateTime.Now.AddSeconds(24 * 13 * 60 * 60) }
        //};
        //NotificationCenter.Current.Show(notification13);
    }

    async void SaveMealInfo(AddedMealInfoMessage addedMealInfo)
    {
        string meal = addedMealInfo.MealInfoStr;

        if (addedMealInfo.IsCanceled)
        {
            App.BotList.Clear();
            StartSetup();
            return;
        }

        await AddAnswer(meal);
        await Task.Delay(300);
        await AddQuestion("Great!");
        await Task.Delay(300);

        var mealModel = new DmmMeal()
        {
            Id = Guid.NewGuid(),
            MealInfo = meal

            //UserId = Guid.Parse(Config.UserId)
        };
        mealModel.Id = Guid.NewGuid();
        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            await UserDialogs.Instance.AlertAsync("Please check your internet connection", "Internet error");
            return;
        }
        try
        {
            UserDialogs.Instance.ShowLoading("Updating...");
            var userMealModel = await DrMuscleRestClient.Instance.AddUserMealAsync(mealModel);
            UserDialogs.Instance.HideLoading();
            if (userMealModel != null)
            {

                if (userMealModel.MealCount == 3)
                {
                    Config.IsMealEntered = true;
                    await UserDialogs.Instance.AlertAsync("Let's customize your meal plan", "Grats on saving 3 meals!", "Ok, customize");
                    FavouriteDiet();
                }
                else
                    AskForNextMeal();
            }
        }
        catch (Exception ex)
        {
            UserDialogs.Instance.HideLoading();

            await UserDialogs.Instance.AlertAsync("Please check your internet connection and try again", "Internet error");

        }

    }

    private async void FavouriteDiet()
    {
        IsKetoSelected = false;
        IsPaleoSelected = false;
        IsVeganSelected = false;
        IsVegetarianSelected = false;
        IsMediterraneanSelected = false;
        IsNoPreferSelected = false;
        await AddQuestion("Favorite diet?");
        App.FavouriteFood = "";
        BtnKeto = await AddCheckbox("Keto", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsKetoSelected = !IsKetoSelected;
            App.FavouriteFood = IsKetoSelected ? "Keto" : "";
            img.Source = IsKetoSelected ? "done.png" : "undone.png";
            LocalDBManager.Instance.SetDBSetting("FavoriteDiet", "Keto");
            ((Image)((StackLayout)((CustomImageButton)BtnPaleo).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegan).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnMediterranean).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnNoPreference).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegetarian).Content).Children[0]).Source = "undone.png";
            IsPaleoSelected = false;
            IsVeganSelected = false;
            IsVegetarianSelected = false;
            IsMediterraneanSelected = false;
            IsNoPreferSelected = false;
        });

        BtnPaleo = await AddCheckbox("Paleo", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsPaleoSelected = !IsPaleoSelected;
            img.Source = IsPaleoSelected ? "done.png" : "undone.png";
            App.FavouriteFood = IsPaleoSelected ? "Paleo" : "";
            LocalDBManager.Instance.SetDBSetting("FavoriteDiet", "Paleo");
            ((Image)((StackLayout)((CustomImageButton)BtnKeto).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegan).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnMediterranean).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnNoPreference).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegetarian).Content).Children[0]).Source = "undone.png";
            IsKetoSelected = false;
            IsVeganSelected = false;
            IsVegetarianSelected = false;
            IsMediterraneanSelected = false;
            IsNoPreferSelected = false;
        });
        BtnVegan = await AddCheckbox("Vegan", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsVeganSelected = !IsVeganSelected;
            img.Source = IsVeganSelected ? "done.png" : "undone.png";
            App.FavouriteFood = IsVeganSelected ? "Vegan" : "";
            LocalDBManager.Instance.SetDBSetting("FavoriteDiet", "Vegan");
            ((Image)((StackLayout)((CustomImageButton)BtnPaleo).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnKeto).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnMediterranean).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnNoPreference).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegetarian).Content).Children[0]).Source = "undone.png";
            IsKetoSelected = false;
            IsPaleoSelected = false;
            IsVegetarianSelected = false;
            IsMediterraneanSelected = false;
            IsNoPreferSelected = false;
        });
        BtnVegetarian = await AddCheckbox("Vegetarian", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsVegetarianSelected = !IsVegetarianSelected;
            img.Source = IsVegetarianSelected ? "done.png" : "undone.png";
            App.FavouriteFood = IsVegetarianSelected ? "Vegetarian" : "";
            LocalDBManager.Instance.SetDBSetting("FavoriteDiet", "Vegetarian");
            ((Image)((StackLayout)((CustomImageButton)BtnPaleo).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnKeto).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegan).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnNoPreference).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnMediterranean).Content).Children[0]).Source = "undone.png";
            IsKetoSelected = false;
            IsPaleoSelected = false;
            IsVeganSelected = false;
            IsMediterraneanSelected = false;
            IsNoPreferSelected = false;
        });
        BtnMediterranean = await AddCheckbox("Mediterranean", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsMediterraneanSelected = !IsMediterraneanSelected;
            img.Source = IsMediterraneanSelected ? "done.png" : "undone.png";
            App.FavouriteFood = IsMediterraneanSelected ? "Mediterranean" : "";
            LocalDBManager.Instance.SetDBSetting("FavoriteDiet", "Mediterranean");
            ((Image)((StackLayout)((CustomImageButton)BtnPaleo).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnKeto).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegan).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnNoPreference).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegetarian).Content).Children[0]).Source = "undone.png";
            IsKetoSelected = false;
            IsPaleoSelected = false;
            IsVeganSelected = false;
            IsVegetarianSelected = false;
            IsNoPreferSelected = false;

        });

        BtnNoPreference = await AddCheckbox("No preference", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsNoPreferSelected = !IsNoPreferSelected;
            img.Source = IsNoPreferSelected ? "done.png" : "undone.png";
            App.FavouriteFood = IsNoPreferSelected ? "No preference" : "";
            LocalDBManager.Instance.SetDBSetting("FavoriteDiet", "No preference");
            ((Image)((StackLayout)((CustomImageButton)BtnPaleo).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnKeto).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegan).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnMediterranean).Content).Children[0]).Source = "undone.png";
            ((Image)((StackLayout)((CustomImageButton)BtnVegetarian).Content).Children[0]).Source = "undone.png";
            IsKetoSelected = false;
            IsPaleoSelected = false;
            IsVeganSelected = false;
            IsVegetarianSelected = false;
            IsMediterraneanSelected = false;

        });

        await AddOptions("Continue", async (sender, ee) =>
        {
            if (string.IsNullOrEmpty(App.FavouriteFood))
                return;
            await AddAnswer(App.FavouriteFood);
            if (Device.RuntimePlatform.Equals(Device.Android))
                await Task.Delay(300);
            ClearOptions();
            //if (FavouriteFood.Equals("Vegetarian"))
            //{
            AskVegiterianOption();
            //}
            //else
            //{
            //    VegetarianEats = "";
            //    AnyAllergies();
            //}
        });
    }

    private async void AskVegiterianOption()
    {
        await CalculateAllMacrosCalculations();
        await AddQuestion("Protein? Select all you like");
        IsFish = false;
        IsSeafood = false;
        IsEggs = false;
        IsDairy = false;
        IsRedMeat = false;
        IsPoultry = false;
        IsNuts = false;
        IsBeans = false;
        IsTofu = false;
        if (!App.FavouriteFood.Equals("Vegetarian") && !App.FavouriteFood.Equals("Vegan"))
            BtnRedMeat = await AddCheckbox("Red meat", (sender, ev) =>
            {
                Image img = (Image)((StackLayout)sender).Children[0];
                IsRedMeat = !IsRedMeat;
                img.Source = IsRedMeat ? "done.png" : "undone.png";
            });
        if (!App.FavouriteFood.Equals("Vegetarian") && !App.FavouriteFood.Equals("Vegan"))
            BtnPoultry = await AddCheckbox("Poultry", (sender, ev) =>
            {
                Image img = (Image)((StackLayout)sender).Children[0];
                IsPoultry = !IsPoultry;
                img.Source = IsPoultry ? "done.png" : "undone.png";
            });
        if (!App.FavouriteFood.Equals("Vegan"))
        {
            BtnFish = await AddCheckbox("Fish", (sender, ev) =>
            {
                Image img = (Image)((StackLayout)sender).Children[0];
                IsFish = !IsFish;
                img.Source = IsFish ? "done.png" : "undone.png";
            });
        }
        if (!App.FavouriteFood.Equals("Vegan"))
        {
            BtnSeafood = await AddCheckbox("Seafood", (sender, ev) =>
            {
                Image img = (Image)((StackLayout)sender).Children[0];
                IsSeafood = !IsSeafood;
                img.Source = IsSeafood ? "done.png" : "undone.png";
            });
        }
        BtnEgg = await AddCheckbox("Eggs", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsEggs = !IsEggs;
            img.Source = IsEggs ? "done.png" : "undone.png";
        });
        BtnDairyProduct = await AddCheckbox("Dairy", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsDairy = !IsDairy;
            img.Source = IsDairy ? "done.png" : "undone.png";
        });
        BtnNuts = await AddCheckbox("Nuts", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsNuts = !IsNuts;
            img.Source = IsNuts ? "done.png" : "undone.png";
        });
        BtnBeans = await AddCheckbox("Beans", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsBeans = !IsBeans;
            img.Source = IsBeans ? "done.png" : "undone.png";
        });
        BtnTofu = await AddCheckbox("Tofu (soy)", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsTofu = !IsTofu;
            img.Source = IsTofu ? "done.png" : "undone.png";
        });
        await AddOptions("Continue", async (sender, ee) =>
        {
            if (!IsFish && !IsSeafood && !IsEggs && !IsDairy && !IsTofu && !IsRedMeat && !IsPoultry && !IsNuts && !IsBeans)
                return;
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                await UserDialogs.Instance.AlertAsync("Please check your internet connection.", "Internet error");
                return;
            }
            var str = "";
            //if (IsRedMeat)
            //    str = "Red meat";
            if (IsRedMeat)
            {
                if (string.IsNullOrEmpty(str))
                {
                    str = "Red meat";
                }
                else
                {
                    str += ", Red meat";
                }
            }
            if (IsPoultry)
            {
                if (string.IsNullOrEmpty(str))
                {
                    str = "Poultry";
                }
                else
                {
                    str += ", Poultry";
                }
            }
            if (IsFish)
            {
                if (string.IsNullOrEmpty(str))
                {
                    str = "Fish";
                }
                else
                {
                    str += ", Fish";
                }
            }
            if (IsSeafood)
            {
                if (string.IsNullOrEmpty(str))
                {
                    str = "Seafood";
                }
                else
                {
                    str += ", Seafood";
                }
            }
            if (IsEggs)
            {
                if (string.IsNullOrEmpty(str))
                    str = "Eggs";
                else
                    str += ", Eggs";
            }
            if (IsDairy)
            {
                if (string.IsNullOrEmpty(str))
                    str = "Dairy";
                else
                    str += ", Dairy";
            }
            if (IsNuts)
            {
                if (string.IsNullOrEmpty(str))
                    str = "Nuts";
                else
                    str += ", Nuts";
            }
            if (IsBeans)
            {
                if (string.IsNullOrEmpty(str))
                    str = "Beans";
                else
                    str += ", Beans";
            }
            if (IsTofu)
            {
                if (string.IsNullOrEmpty(str))
                    str = "Tofu (soy)";
                else
                    str += ", Tofu (soy)";
            }
            var data = "";
            if (!string.IsNullOrEmpty(str))
            {
                var array = str.Split(',');
                if (array.Count() > 0)
                {
                    //str = str.Replace($", {array.Last()}", $", and{array.Last()}");
                    data = string.Join(Environment.NewLine, array.Select(element => element.Trim()));
                }
                else
                {
                    data = str;
                }
                str = $"{str}";
            }
            await AddAnswer(data);
            App.VegetarianEats = str;
            CheckUnselectedSources();
            if (Device.RuntimePlatform.Equals(Device.Android))
                await Task.Delay(300);
            ClearOptions();
            await CountryName();
            //AskForSimpleMeal();
        });
        lstChats.ScrollTo(App.BotList.LastOrDefault(), ScrollToPosition.End, animate: false);
    }
    private async Task CountryName()
    {
        await AddQuestion("Country?");
        var countryDetails = new MealGeneralPopup();
        countryDetails.SetPopupTitle("Country", GeneralPopupEnum.Country, "", "");
        IsBodyweightPopup = true;
        if (countryDetails != null)
        {
            await PopupNavigation.Instance.PushAsync(countryDetails);
        }
        else
        {
            DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");
            return;
        }
        ClearOptions();
    }

    private void CheckUnselectedSources()
    {
        string[] separator = { ", " };
        string[] allItems = totalSources.Split(separator, StringSplitOptions.None);
        string[] selectedItems = App.VegetarianEats.Split(separator, StringSplitOptions.None);

        // Find unselected items
        var unselectedItems = allItems.Except(selectedItems);

        // Join the unselected items into a single string
        unselectedSource = string.Join(", ", unselectedItems);

    }

    private async Task CalculateAllMacrosCalculations()
    {
        if (LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value != null)
        {
            _targetIntake = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("TargetIntake").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
        }
        _targetIntake = Math.Round(_targetIntake);

        if (App.FavouriteFood == "Mediterranean")
        {
            // 15-20% pro, 45-55% carb, 30-35% fat
            //  60-70% carb, 30-40% fat
            await CalculateAllMacrosByDietType(0.15m, 0.20m, 0.60m, 0.70m, 0.30m, 0.40m);
        }
        else if (App.FavouriteFood == "Vegetarian")
        {
            // 20-30% pro, 40-50% carb, 30-35% fat
            // // 55-65% carb, 35-45% fat
            await CalculateAllMacrosByDietType(0.20m, 0.30m, 0.55m, 0.65m, 0.35m, 0.45m);
        }
        else if (App.FavouriteFood == "Vegan")
        {
            // 12-22% pro, 50-75% carb, 12-17% fat
            // 18-25% pro, 50-75% carb, 12-17% fat
            // 65-75% carb, 25-35% fat
            await CalculateAllMacrosByDietType(0.18m, 0.25m, 0.65m, 0.75m, 0.25m, 0.35m);
        }
        else if (App.FavouriteFood == "Paleo")
        {
            // 27-33% pro, 25-37% carb, 32-42% fat
            // 35-45% carb, 55-65% fat
            await CalculateAllMacrosByDietType(0.27m, 0.33m, 0.35m, 0.45m, 0.55m, 0.65m);
        }
        else if (App.FavouriteFood == "Keto")
        {
            // 20-33% pro, 5-15% carb, 60-73% fat
            // 20-30% carb, 70-80% fat
            await CalculateAllMacrosByDietType(0.20m, 0.33m, 0.20m, 0.30m, 0.70m, 0.80m);
        }
        else
        {
            // Protein ranges
            MatchCollection matches = Regex.Matches(ProteinRecom, @"\d+");
            MatchCollection matches1 = Regex.Matches(CarbRecom, @"\d+");
            MatchCollection matches2 = Regex.Matches(FatRecom, @"\d+");
            if (matches.Count >= 2)
            {
                App.minPro = int.Parse(matches[0].Value);
                App.maxPro = int.Parse(matches[1].Value);
            }
            // Carb ranges
            if (matches.Count >= 2)
            {
                App.minCarb = int.Parse(matches1[0].Value);
                App.maxCarb = int.Parse(matches1[1].Value);
            }
            // Protein ranges
            if (matches.Count >= 2)
            {
                App.minFat = int.Parse(matches2[0].Value);
                App.maxFat = int.Parse(matches2[1].Value);
            }
        }

        await CalculateMacros(_targetIntake, App.minPro, App.maxPro, App.minCarb, App.maxCarb, App.minFat, App.maxFat);
    }



    private async void AnyAllergies()
    {

        await AddQuestion("Any allergies?");


        var btn2 = new DrMuscleButton()
        {
            Text = "Yes, allergies",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 2),
            CornerRadius = 0
        };
        btn2.Clicked += async (o, ev) => {

            await AddAnswer("Yes, allergies");
            App.IsAnyAllergies = true;
            var alleryPp = new MealGeneralPopup();
            alleryPp.SetPopupTitle("Enter allergies", GeneralPopupEnum.Allergy, "", "List all allergies");
            IsBodyweightPopup = true;
            if (alleryPp != null)
            {
                await PopupNavigation.Instance.PushAsync(alleryPp);
            }
            else
            {
                DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");
                return;
            }
            ClearOptions();
        };
        stackOptions.Children.Add(btn2);


        await AddOptions("No allergies", async (sender, e) => {
            await AddAnswer("No allergies");
            App.IsAnyAllergies = false;
            App.AllergyText = "";
            ClearOptions();
            FoodsYouDontLikes();
            //SyncWithServer();
            //HowManyExercise();
            //ClearOptions();
        });

        //await AddOptions("Yes, allergies", async (sender, e) => {
        //    await AddAnswer("Yes, allergies");
        //    IsAnyAllergies = true;
        //    var alleryPp = new MealGeneralPopup();
        //    alleryPp.SetPopupTitle("Enter allergies", GeneralPopupEnum.Allergy, "", "List all allergies");
        //    IsBodyweightPopup = true;
        //    PopupNavigation.Instance.PushAsync(alleryPp);
        //    ClearOptions();
        //});
        await Task.Delay(300);
        lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.End, animate: false);
    }
    private async void FoodsYouDontLikes()
    {
        await AddQuestion("Foods you don't like?");
        var btn2 = new DrMuscleButton()
        {
            Text = "Yes",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 2),
            CornerRadius = 0
        };
        btn2.Clicked += async (o, ev) => {
            await AddAnswer("Yes");
            App.IsAnyFoodYouDontLike = true;
            var alleryPp = new MealGeneralPopup();
            alleryPp.SetPopupTitle("Enter foods you don't like", GeneralPopupEnum.FoodsYouDontLike, "", "List all foods you don't like");
            //IsBodyweightPopup = true;
            if (alleryPp != null)
            {
                await PopupNavigation.Instance.PushAsync(alleryPp);
            }
            else
            {
                DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");
                return;
            }
            ClearOptions();
        };
        stackOptions.Children.Add(btn2);
        await AddOptions("No", async (sender, e) => {
            await AddAnswer("No");
            App.IsAnyFoodYouDontLike = false;
            App.FoodsYouDontLikeText = "";
            ClearOptions();
            RecommendedCalories();
        });
        await Task.Delay(300);
        lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.End, animate: false);
    }

    private async void RecommendedCalories()
    {
        double targetValue = (double)Math.Round(_targetIntake);

        double fivePercent = targetValue * 0.10;

        // Calculate 10% higher value
        double upperValue = targetValue + fivePercent;

        // Calculate 10% lower value
        double lowerValue = targetValue - fivePercent;
        MinimumCalories = Math.Round(lowerValue);
        MaximumCalories = Math.Round(upperValue);
        if (App.FavouriteFood == "No preference")
        {
            await AddQuestion($"Recommended for you: \r\n - {MinimumCalories}-{MaximumCalories} calories \r\n - {ProteinRecom} protein \r\n - {CarbRecom} carbs \r\n - {FatRecom} fat");
        }
        else
        {
            await AddQuestion($"Recommended for you: \r\n - {MinimumCalories}-{MaximumCalories} calories \r\n - {App.minPro}-{App.maxPro} g protein \r\n - {App.minCarb}-{App.maxCarb} g carbs \r\n - {App.minFat}-{App.maxFat} g fat");
        }

        await AddOptions("Sounds good", async (sender, e) =>
        {
            await AddAnswer("Sounds good");
            ClearOptions();
            RecommendedMeal();
        });
        await Task.Delay(300);
        lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.End, animate: false);
    }

    private async void ChangeMealsRecommendation()
    {
        int TotalMeals = 0;
        IsBreakfast = false;
        IsLunch = false;
        IsDinner = false;
        IsSnack1 = false;
        IsSnack2 = false;
        IsProteinShake = false;

        BtnBreakfast = await AddCheckbox("Breakfast", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsBreakfast = !IsBreakfast;
            TotalMeals += (IsBreakfast) ? 1 : -1;
            img.Source = IsBreakfast ? "done.png" : "undone.png";

        });
        BtnLunch = await AddCheckbox("Lunch", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsLunch = !IsLunch;
            TotalMeals += (IsLunch) ? 1 : -1;
            img.Source = IsLunch ? "done.png" : "undone.png";

        });

        BtnDinner = await AddCheckbox("Dinner", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsDinner = !IsDinner;
            TotalMeals += (IsDinner) ? 1 : -1;
            img.Source = IsDinner ? "done.png" : "undone.png";

        });

        BtnSnack1 = await AddCheckbox("Snack 1", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsSnack1 = !IsSnack1;
            TotalMeals += (IsSnack1) ? 1 : -1;
            img.Source = IsSnack1 ? "done.png" : "undone.png";
        });

        BtnSnack2 = await AddCheckbox("Snack 2", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsSnack2 = !IsSnack2;
            TotalMeals += (IsSnack2) ? 1 : -1;
            img.Source = IsSnack2 ? "done.png" : "undone.png";
        });
        BtnProteinShake = await AddCheckbox("Protein shake", (sender, ev) =>
        {
            Image img = (Image)((StackLayout)sender).Children[0];
            IsProteinShake = !IsProteinShake;
            TotalMeals += (IsProteinShake) ? 1 : -1;
            img.Source = IsProteinShake ? "done.png" : "undone.png";

        });
        bool isRequestInProgress = false;
        await AddOptions("Continue", async (sender, e) =>
        {
            try
            {

                if (!IsBreakfast && !IsLunch && !IsDinner && !IsSnack1 && !IsSnack2 && !IsProteinShake)
                    return;
                if (!IsBreakfast && !IsLunch && !IsDinner && !IsSnack1 && !IsSnack2 && IsProteinShake)
                {
                    // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     Message = "Please select at least one meal.",
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                    // });
                    await HelperClass.DisplayCustomPopupForResult("",
                            "Please select at least one meal.","Ok","");
                    return;
                }
                App.IsProteinShakeExist = (IsProteinShake) ? true : false;
                //if (TotalMeals < 3)
                //{
                //    CrossToastPopUp.Current.ShowToastMessage("Please select minimum 3 meals", ToastLength.Short);
                //    return;
                //}
                if (IsBreakfast && IsLunch && IsDinner && IsProteinShake && !IsSnack1 && !IsSnack2)
                    IsDefaultMeals = true;
                else
                    IsDefaultMeals = false;
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    await UserDialogs.Instance.AlertAsync("Please check your internet connection.", "Internet error");
                    return;
                }
                var str = "";
                //if (IsRedMeat)
                //    str = "Red meat";

                if (IsBreakfast)
                {
                    if (string.IsNullOrEmpty(str))
                    {
                        str = "Breakfast";
                    }
                    else
                    {
                        str += ", Breakfast";
                    }
                }

                if (IsLunch)
                {
                    if (string.IsNullOrEmpty(str))
                    {
                        str = "Lunch";
                    }
                    else
                    {
                        str += ", Lunch";
                    }
                }

                if (IsDinner)
                {
                    if (string.IsNullOrEmpty(str))
                    {
                        str = "Dinner";
                    }
                    else
                    {
                        str += ", Dinner";
                    }
                }
                if (IsSnack1)
                {
                    if (string.IsNullOrEmpty(str))
                        str = "Snack 1";
                    else
                        str += ", Snack 1";
                }
                if (IsSnack2)
                {
                    if (string.IsNullOrEmpty(str))
                        str = "Snack 2";
                    else
                        str += ", Snack 2";
                }
                if (IsProteinShake)
                {
                    if (string.IsNullOrEmpty(str))
                        str = "Protein shake";
                    else
                        str += ", Protein shake";
                }
                var data = "";
                if (!string.IsNullOrEmpty(str))
                {
                    var array = str.Split(',');
                    if (array.Count() > 0)
                    {
                        //str = str.Replace($", {array.Last()}", $", and{array.Last()}");
                        data = string.Join(Environment.NewLine, array.Select(element => element.Trim()));
                    }
                    else
                    {
                        data = str;
                    }
                    str = $"{str}";
                }
                if (isRequestInProgress)
                {
                    return;
                }
                isRequestInProgress = true;

                try
                {
                    await AddAnswer(data);
                    App.MealTtypes = str;
                    if (Device.RuntimePlatform.Equals(Device.Android))
                        await Task.Delay(300);
                    ClearOptions();
                    await CalculateAllMacros();
                    await SetMacrosLocally();
                    SyncWithServer();
                }
                finally
                {
                    isRequestInProgress = false;
                }
            }
            catch (Exception ex)
            {

            }
        });
        await Task.Delay(300);
        lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.End, animate: false);
    }

    private async Task CalculateAllMacros()
    {
        _targetIntake = Math.Round(_targetIntake);



        if (IsDefaultMeals)
        {
            App.ProteinShakeProteinGrams = 30;
            calculatedProteins = calculatedProteins - App.ProteinShakeProteinGrams;
            calculatedCarbs = calculatedCarbs - App.ProteinShakeCarbsGrams;
            calculatedFats = calculatedFats - App.ProteinShakeFatGrams;
            App.BreakfastMinProteinGrams = (App.minPro - 5) * 40 / 100;
            App.BreakfastMaxProteinGrams = (App.maxPro - 5) * 40 / 100;
            App.BreakfastMinCarbsGrams = App.minCarb * 30 / 100;
            App.BreakfastMaxCarbsGrams = App.maxCarb * 30 / 100;
            App.BreakfastMinFatGrams = App.minFat * 30 / 100;
            App.BreakfastMaxFatGrams = App.maxFat * 30 / 100;
            App.BreakfastMinCalories = (App.BreakfastMinProteinGrams * 4) + (App.BreakfastMinCarbsGrams * 4) + (App.BreakfastMinFatGrams * 9);
            App.BreakfastMaxCalories = (App.BreakfastMaxProteinGrams * 4) + (App.BreakfastMaxCarbsGrams * 4) + (App.BreakfastMaxFatGrams * 9);
            
            App.LunchMinProteinGrams = (App.minPro - 5) * 30 / 100;
            App.LunchMaxProteinGrams = (App.maxPro - 5) * 30 / 100;
            App.LunchMinCarbsGrams = App.minCarb * 35 / 100;
            App.LunchMaxCarbsGrams = App.maxCarb * 35 / 100;
            App.LunchMinFatGrams = App.minFat * 25 / 100;
            App.LunchMaxFatGrams = App.maxFat * 25 / 100;
            App.LunchMinCalories = (App.LunchMinProteinGrams * 4) + (App.LunchMinCarbsGrams * 4) + (App.LunchMinFatGrams * 9);
            App.LunchMaxCalories = (App.LunchMaxProteinGrams * 4) + (App.LunchMaxCarbsGrams * 4) + (App.LunchMaxFatGrams * 9);
            
            App.DinnerMinProteinGrams = (App.minPro - 5) * 30 / 100;
            App.DinnerMaxProteinGrams = (App.maxPro - 5) * 30 / 100;
            App.DinnerMinCarbsGrams = App.minCarb * 35 / 100;
            App.DinnerMaxCarbsGrams = App.maxCarb * 35 / 100;
            App.DinnerMinFatGrams = App.minFat * 45 / 100;
            App.DinnerMaxFatGrams = App.maxFat * 45 / 100;
            App.DinnerMinCalories = (App.DinnerMinProteinGrams * 4) + (App.DinnerMinCarbsGrams * 4) + (App.DinnerMinFatGrams * 9);
            App.DinnerMaxCalories = (App.DinnerMaxProteinGrams * 4) + (App.DinnerMaxCarbsGrams * 4) + (App.DinnerMaxFatGrams * 9);


            App.Snack1MinProteinGrams = 0;
            App.Snack1MaxProteinGrams = 0;
            App.Snack1MinCarbsGrams = 0;
            App.Snack1MaxCarbsGrams = 0;
            App.Snack1MinFatGrams = 0;
            App.Snack1MaxFatGrams = 0;
            App.Snack1MinCalories = 0;
            App.Snack1MaxCalories = 0;
            
            App.Snack2MinProteinGrams = 0;
            App.Snack2MaxProteinGrams = 0;
            App.Snack2MinCarbsGrams = 0;
            App.Snack2MaxCarbsGrams = 0;
            App.Snack2MinFatGrams = 0;
            App.Snack2MaxFatGrams = 0;
            App.Snack2MinCalories = 0;
            App.Snack2MaxCalories = 0;
            
            //ProteinShakeProteinGrams = calculatedProteins * 10 / 100;
            App.ProteinShakeCalories = (App.ProteinShakeProteinGrams * 4) + (App.ProteinShakeCarbsGrams * 4) + (App.ProteinShakeFatGrams * 9);
        }
        else
        {
            App.ProteinShakeProteinGrams = IsProteinShake ? 30 : 0;
            App.ProteinShakeCarbsGrams = IsProteinShake ? 12 : 0;
            App.ProteinShakeFatGrams = IsProteinShake ? 4 : 0;

            calculatedProteins = calculatedProteins - App.ProteinShakeProteinGrams;
            calculatedCarbs = calculatedCarbs - App.ProteinShakeCarbsGrams;
            calculatedFats = calculatedFats - App.ProteinShakeFatGrams;
            var array = App.MealTtypes.Split(',');
            if (array?.Length > 0)
            {
                decimal proteinPercentageValue = 0;
                decimal carbsPercentageValue = 0;
                decimal fatPercentageValue = 0;
                decimal snackValue = 0;
                if ((array?.Length > 2 && IsSnack1 && IsSnack2 && !IsProteinShake) || (array?.Length > 3 && IsSnack1 && IsSnack2 && IsProteinShake))
                {
                    snackValue = 10;
                    proteinPercentageValue = 80 / ((IsProteinShake) ? array.Length - 3 : array.Length - 2);
                    carbsPercentageValue = proteinPercentageValue;
                    fatPercentageValue = proteinPercentageValue;
                }
                else if ((array?.Length > 2 && IsSnack1) || (array?.Length > 2 && IsSnack2))
                {
                    snackValue = 20;
                    proteinPercentageValue = 80 / ((IsProteinShake) ? array.Length - 2 : array.Length - 1);
                    carbsPercentageValue = proteinPercentageValue;
                    fatPercentageValue = proteinPercentageValue;
                }
                else
                {
                    proteinPercentageValue = CalculateRatioByLength((IsProteinShake) ? array.Length - 1 : array.Length);
                    carbsPercentageValue = proteinPercentageValue;
                    fatPercentageValue = proteinPercentageValue;
                }


                for (var i = 0; i <= array.Length; i++)
                {
                    if (IsBreakfast)
                    {
                        CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.BreakfastMinProteinGrams, out App.BreakfastMinCarbsGrams, out App.BreakfastMinFatGrams, out App.BreakfastMinCalories, out App.BreakfastMaxProteinGrams, out App.BreakfastMaxCarbsGrams, out App.BreakfastMaxFatGrams, out App.BreakfastMaxCalories);
                    }
                    if (IsLunch)
                    {
                        CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.LunchMinProteinGrams, out App.LunchMinCarbsGrams, out App.LunchMinFatGrams, out App.LunchMinCalories, out App.LunchMaxProteinGrams, out App.LunchMaxCarbsGrams, out App.LunchMaxFatGrams, out App.LunchMaxCalories);
                    }
                    if (IsDinner)
                    {
                        CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.DinnerMinProteinGrams, out App.DinnerMinCarbsGrams, out App.DinnerMinFatGrams, out App.DinnerMinCalories, out App.DinnerMaxProteinGrams, out App.DinnerMaxCarbsGrams, out App.DinnerMaxFatGrams, out App.DinnerMaxCalories);
                    }
                    if (IsSnack1)
                    {
                        if (snackValue > 0)
                            CalculateMealNutrients(snackValue, snackValue, snackValue, out App.Snack1MinProteinGrams, out App.Snack1MinCarbsGrams, out App.Snack1MinFatGrams, out App.Snack1MinCalories, out App.Snack1MaxProteinGrams, out App.Snack1MaxCarbsGrams, out App.Snack1MaxFatGrams, out App.Snack1MaxCalories);
                        else
                            CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.Snack1MinProteinGrams, out App.Snack1MinCarbsGrams, out App.Snack1MinFatGrams, out App.Snack1MinCalories, out App.Snack1MaxProteinGrams, out App.Snack1MaxCarbsGrams, out App.Snack1MaxFatGrams, out App.Snack1MaxCalories);
                    }
                    if (IsSnack2)
                    {
                        if (snackValue > 0)
                            CalculateMealNutrients(snackValue, snackValue, snackValue, out App.Snack2MinProteinGrams, out App.Snack2MinCarbsGrams, out App.Snack2MinFatGrams, out App.Snack2MinCalories, out App.Snack2MaxProteinGrams, out App.Snack2MaxCarbsGrams, out App.Snack2MaxFatGrams, out App.Snack2MaxCalories);
                        else
                            CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.Snack2MinProteinGrams, out App.Snack2MinCarbsGrams, out App.Snack2MinFatGrams, out App.Snack2MinCalories, out App.Snack2MaxProteinGrams, out App.Snack2MaxCarbsGrams, out App.Snack2MaxFatGrams, out App.Snack2MaxCalories);
                    }
                    if (IsProteinShake)
                    {
                        App.ProteinShakeCalories = (App.ProteinShakeProteinGrams * 4) + (App.ProteinShakeCarbsGrams * 4) + (App.ProteinShakeFatGrams * 9);
                    }
                }
            }

        }

    }
    public static decimal CalculateRatioByLength(int length)
    {
        return 100 / length;
    }
    private async Task CalculateAllMacrosByDietType(decimal proteinPercentageMin, decimal proteinPercentageMax, decimal carbPercentageMin, decimal carbPercentageMax, decimal fatPercentageMin, decimal fatPercentageMax)
    {

        // Calorie-per-gram values
        int proteinCaloriesPerGram = 4;
        int carbCaloriesPerGram = 4;
        int fatCaloriesPerGram = 9;

        decimal _userBodyWeight = 0;
        if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
        {
            _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value, CultureInfo.InvariantCulture);
        }
        //var protein = $"{Math.Round(_userBodyWeight * (decimal)1.8)} - {Math.Round(_userBodyWeight * (decimal)2.5)} g";
        var newLowerTargetIntake = _targetIntake - (_userBodyWeight * (decimal)2.15) * 4;
        var newHigherTargetIntake = _targetIntake - (_userBodyWeight * (decimal)2.15) * 4;

        //var carbs = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 4)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 4)} g";
        //macros += $"\nCarbs range: {carbs}";

        //var fats = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 9)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 9)} g";
        //macros += $"\nFats range: {fats}";

        // Calculate calories for proteins
        //int minProteinCalories = (int)(_targetIntake * proteinPercentageMin);
        //int maxProteinCalories = (int)(_targetIntake * proteinPercentageMax);
        int minProteinCalories = (int)Math.Round(_userBodyWeight * (decimal)1.8);
        int maxProteinCalories = (int)Math.Round(_userBodyWeight * (decimal)2.5);

        // Calculate grams for proteins
        App.minPro = minProteinCalories;
        App.maxPro = maxProteinCalories;

        // Calculate calories for carbs
        //int minCarbCalories = (int)(_targetIntake * carbPercentageMin);
        //int maxCarbCalories = (int)(_targetIntake * carbPercentageMax);
        var minCarbCalories = (newLowerTargetIntake * carbPercentageMin) / carbCaloriesPerGram;
        var maxCarbCalories = (newHigherTargetIntake * carbPercentageMax) / carbCaloriesPerGram;

        // Calculate grams for carbs
        App.minCarb = (int)Math.Round(minCarbCalories);
        App.maxCarb = (int)Math.Round(maxCarbCalories);

        // Calculate calories for fats
        //int minFatCalories = (int)(_targetIntake * fatPercentageMin);
        //int maxFatCalories = (int)(_targetIntake * fatPercentageMax);
        var minFatCalories = (newLowerTargetIntake * fatPercentageMin) / fatCaloriesPerGram;
        var maxFatCalories = (newHigherTargetIntake * fatPercentageMax) / fatCaloriesPerGram;

        // Calculate grams for fats
        App.minFat = (int)Math.Round(minFatCalories);
        App.maxFat = (int)Math.Round(maxFatCalories);
    }

    void CalculateMealNutrients(decimal proteinRatio, decimal carbsRatio, decimal fatRatio, out decimal minprotein, out decimal mincarbs, out decimal minfat, out decimal mincalories, out decimal maxprotein, out decimal maxcarbs, out decimal maxfat, out decimal maxcalories)
    {
        minprotein = App.minPro * proteinRatio / 100;
        maxprotein = App.maxPro * proteinRatio / 100;
        mincarbs = App.minCarb * carbsRatio / 100;
        maxcarbs = App.maxCarb * carbsRatio / 100;
        minfat = App.minFat * fatRatio / 100;
        maxfat = App.maxFat * fatRatio / 100;
        mincalories = (minprotein * 4) + (mincarbs * 4) + (minfat * 9);
        maxcalories = (maxprotein * 4) + (maxcarbs * 4) + (maxfat * 9);

        //protein = calculatedProteins * proteinRatio / 100;
        //carbs = calculatedCarbs * carbsRatio / 100;
        //fat = calculatedFats * fatRatio / 100;
        //calories = (protein * 4) + (carbs * 4) + (fat * 9);
    }

    private async void RecommendedMeal()
    {
        await AddQuestion($"Recommended for you: \r\n - 1 breakfast \r\n - 1 lunch \r\n - 1 dinner \r\n - 1 protein shake");

        var btn2 = new DrMuscleButton()
        {
            Text = "Change",
            TextColor = Color.FromHex("#195377"),
            BackgroundColor = Colors.Transparent,
            HeightRequest = 55,
            BorderWidth = 2,
            BorderColor = AppThemeConstants.BlueColor,
            Margin = new Thickness(25, 2),
            CornerRadius = 0
        };
        btn2.Clicked += async (o, ev) =>
        {

            //await AddAnswer("Change");
            ClearOptions();
            IsDefaultMeals = false;
            ChangeMealsRecommendation();
        };
        bool isRequestInProgress = false;
        stackOptions.Children.Add(btn2);
        await AddOptions("Sounds good", async (sender, e) =>
        {
            if (isRequestInProgress)
            {
                return;
            }
            isRequestInProgress = true;

            try
            {
                IsDefaultMeals = true;
                App.MealTtypes = "Breakfast, Lunch, Dinner, Protein shake";
                await AddAnswer("Sounds good");
                await CalculateAllMacros();
                ClearOptions();
                await SetMacrosLocally();
                SyncWithServer();
            }
            finally
            {
                isRequestInProgress = false;
            }
        });
        await Task.Delay(300);
        lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.End, animate: false);
    }
    private async Task SetMacrosLocally()
    {
        MacrosDistribution macrosDistribution = new MacrosDistribution();
        macrosDistribution.TargetIntake = _targetIntake;
        macrosDistribution.MinPro = App.minPro;
        macrosDistribution.MaxPro = App.maxPro;
        macrosDistribution.MinCarb = App.minCarb;
        macrosDistribution.MaxCarb = App.maxCarb;
        macrosDistribution.MinFat = App.minFat;
        macrosDistribution.MaxFat = App.maxFat;

        macrosDistribution.BreakfastMinCalories = App.BreakfastMinCalories;
        macrosDistribution.BreakfastMaxCalories = App.BreakfastMaxCalories;
        macrosDistribution.BreakfastMinProteinGrams = App.BreakfastMinProteinGrams;
        macrosDistribution.BreakfastMaxProteinGrams = App.BreakfastMaxProteinGrams;
        macrosDistribution.BreakfastMinCarbsGrams = App.BreakfastMinCarbsGrams;
        macrosDistribution.BreakfastMaxCarbsGrams = App.BreakfastMaxCarbsGrams;
        macrosDistribution.BreakfastMinFatGrams = App.BreakfastMinFatGrams;
        macrosDistribution.BreakfastMaxFatGrams = App.BreakfastMaxFatGrams;

        macrosDistribution.LunchMinCalories = App.LunchMinCalories;
        macrosDistribution.LunchMaxCalories = App.LunchMaxCalories;
        macrosDistribution.LunchMinProteinGrams = App.LunchMinProteinGrams;
        macrosDistribution.LunchMaxProteinGrams = App.LunchMaxProteinGrams;
        macrosDistribution.LunchMinCarbsGrams = App.LunchMinCarbsGrams;
        macrosDistribution.LunchMaxCarbsGrams = App.LunchMaxCarbsGrams;
        macrosDistribution.LunchMinFatGrams = App.LunchMinFatGrams;
        macrosDistribution.LunchMaxFatGrams = App.LunchMaxFatGrams;

        macrosDistribution.DinnerMinCalories = App.DinnerMinCalories;
        macrosDistribution.DinnerMaxCalories = App.DinnerMaxCalories;
        macrosDistribution.DinnerMinProteinGrams = App.DinnerMinProteinGrams;
        macrosDistribution.DinnerMaxProteinGrams = App.DinnerMaxProteinGrams;
        macrosDistribution.DinnerMinCarbsGrams = App.DinnerMinCarbsGrams;
        macrosDistribution.DinnerMaxCarbsGrams = App.DinnerMaxCarbsGrams;
        macrosDistribution.DinnerMinFatGrams = App.DinnerMinFatGrams;
        macrosDistribution.DinnerMaxFatGrams = App.DinnerMaxFatGrams;

        macrosDistribution.ProteinShakeCalories = App.ProteinShakeCalories;
        macrosDistribution.ProteinShakeProteinGrams = App.ProteinShakeProteinGrams;
        macrosDistribution.ProteinShakeCarbsGrams = App.ProteinShakeCarbsGrams;
        macrosDistribution.ProteinShakeFatGrams = App.ProteinShakeFatGrams;

        macrosDistribution.Snack1MinCalories = App.Snack1MinCalories;
        macrosDistribution.Snack1MaxCalories = App.Snack1MaxCalories;
        macrosDistribution.Snack1MinProteinGrams = App.Snack1MinProteinGrams;
        macrosDistribution.Snack1MaxProteinGrams = App.Snack1MaxProteinGrams;
        macrosDistribution.Snack1MinCarbsGrams = App.Snack1MinCarbsGrams;
        macrosDistribution.Snack1MaxCarbsGrams = App.Snack1MaxCarbsGrams;
        macrosDistribution.Snack1MinFatGrams = App.Snack1MinFatGrams;
        macrosDistribution.Snack1MaxFatGrams = App.Snack1MaxFatGrams;

        macrosDistribution.Snack2MinCalories = App.Snack2MinCalories;
        macrosDistribution.Snack2MaxCalories = App.Snack2MaxCalories;
        macrosDistribution.Snack2MinProteinGrams = App.Snack2MinProteinGrams;
        macrosDistribution.Snack2MaxProteinGrams = App.Snack2MaxProteinGrams;
        macrosDistribution.Snack2MinCarbsGrams = App.Snack2MinCarbsGrams;
        macrosDistribution.Snack2MaxCarbsGrams = App.Snack2MaxCarbsGrams;
        macrosDistribution.Snack2MinFatGrams = App.Snack2MinFatGrams;
        macrosDistribution.Snack2MaxFatGrams = App.Snack2MaxFatGrams;

        LocalDBManager.Instance.SetDBSetting("MacrosDistribution", JsonConvert.SerializeObject(macrosDistribution));

    }
    //private async void HowManyExercise()
    //{
    //    ExericseTime = "";
    //    await AddQuestion("How many times a week do you exercise?");
    //    BtnIdontExercise = await AddCheckbox("I don't exercise", (sender, ev) =>
    //    {
    //        Image img = (Image)((StackLayout)sender).Children[0];
    //        img.Source = "done.png";
    //        ExericseTime = "I don't exercise";
    //        ((Image)((StackLayout)((CustomImageButton)Btn12Times).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)Btn34Times).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)Btn5PlusTimes).Content).Children[0]).Source = "undone.png";
    //    });
    //    Btn12Times = await AddCheckbox("1-2 times", (sender, ev) =>
    //    {
    //        Image img = (Image)((StackLayout)sender).Children[0];
    //        img.Source = "done.png";
    //        ExericseTime = "1-2 times";
    //        ((Image)((StackLayout)((CustomImageButton)Btn34Times).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)Btn5PlusTimes).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)BtnIdontExercise).Content).Children[0]).Source = "undone.png";

    //    });

    //    Btn34Times = await AddCheckbox("3-4 times", (sender, ev) =>
    //    {
    //        Image img = (Image)((StackLayout)sender).Children[0];
    //        img.Source = "done.png";
    //        ExericseTime = "3-4 times";
    //        ((Image)((StackLayout)((CustomImageButton)Btn12Times).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)Btn5PlusTimes).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)BtnIdontExercise).Content).Children[0]).Source = "undone.png";
    //    });
    //    Btn5PlusTimes = await AddCheckbox("5+ times", (sender, ev) =>
    //    {
    //        Image img = (Image)((StackLayout)sender).Children[0];
    //        img.Source = "done.png";
    //        ExericseTime = "5+ times";
    //        ((Image)((StackLayout)((CustomImageButton)Btn12Times).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)Btn34Times).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)BtnIdontExercise).Content).Children[0]).Source = "undone.png";
    //    });

    //    await AddOptions("Continue", async (sender, ee) =>
    //    {
    //        if (string.IsNullOrEmpty(ExericseTime))
    //            return;
    //        await AddAnswer(ExericseTime);
    //        if (Device.RuntimePlatform.Equals(Device.Android))
    //            await Task.Delay(300);
    //        ClearOptions();
    //        HowActive();
    //    });


    //}

    //private async void HowActive()
    //{
    //    ActiveOnJob = "";
    //    await AddQuestion("How active is your job (or daily occupation)?");
    //    BtnIMostlySit = await AddCheckbox("I mostly sit", (sender, ev) =>
    //    {
    //        Image img = (Image)((StackLayout)sender).Children[0];
    //        img.Source = "done.png";
    //        ActiveOnJob = "I mostly sit";
    //        ((Image)((StackLayout)((CustomImageButton)BtnIMostlyStandWalk).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)BtnIdoManualOrPhysicalWork).Content).Children[0]).Source = "undone.png";

    //    });

    //    BtnIMostlyStandWalk = await AddCheckbox("I mostly stand or walk", (sender, ev) =>
    //    {
    //        Image img = (Image)((StackLayout)sender).Children[0];
    //        img.Source = "done.png";
    //        ActiveOnJob = "I mostly stand or walk";
    //        ((Image)((StackLayout)((CustomImageButton)BtnIMostlySit).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)BtnIdoManualOrPhysicalWork).Content).Children[0]).Source = "undone.png";
    //    });
    //    BtnIdoManualOrPhysicalWork = await AddCheckbox("I do manual or physical work", (sender, ev) =>
    //    {
    //        Image img = (Image)((StackLayout)sender).Children[0];
    //        img.Source = "done.png";
    //        ActiveOnJob = "I do manual or physical work";
    //        ((Image)((StackLayout)((CustomImageButton)BtnIMostlySit).Content).Children[0]).Source = "undone.png";
    //        ((Image)((StackLayout)((CustomImageButton)BtnIMostlyStandWalk).Content).Children[0]).Source = "undone.png";
    //    });

    //    await AddOptions("Continue", async (sender, ee) =>
    //    {
    //        if (string.IsNullOrEmpty(ActiveOnJob))
    //            return;
    //        if (!CrossConnectivity.Current.IsConnected)
    //        {
    //            await UserDialogs.Instance.AlertAsync("Please check your internet connection", "Internet error");
    //            return;
    //        }
    //        await AddAnswer(ActiveOnJob);
    //        if (Device.RuntimePlatform.Equals(Device.Android))
    //            await Task.Delay(300);
    //        ClearOptions();
    //        SyncWithServer();
    //    });

    //}
    public async Task CalculateMacros(decimal totalCalories, int minPro, int maxPro, int minCarb, int maxCarb, int minFat, int maxFat)
    {
        double proteinCaloriesPerGram = 4;
        double carbCaloriesPerGram = 4;
        double fatCaloriesPerGram = 9;

        // Nutrient ranges
        //int minProteins = 91;
        //int maxProteins = 125;

        //int minCarbs = 219;
        //int maxCarbs = 317;

        //int minFats = 42;
        //int maxFats = 73;

        // Calculate possible combinations
        try
        {
            for (int proteins = minPro; proteins <= maxPro; proteins++)
            {
                for (int carbs = minCarb; carbs <= maxCarb; carbs++)
                {
                    for (int fats = minFat; fats <= maxFat; fats++)
                    {
                        // Check if the combination meets the total calorie constraint
                        int totalCaloriesForCombination = (int)((proteins * proteinCaloriesPerGram) + (carbs * carbCaloriesPerGram) + (fats * fatCaloriesPerGram));

                        if (totalCaloriesForCombination == totalCalories)
                        {
                            calculatedProteins = proteins;
                            calculatedCarbs = carbs;
                            calculatedFats = fats;
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {

        }
    }
    private async void SyncWithServer(bool checkForExistingFoods = false)
    {
        App.BotList?.Clear();
        LocalDBManager.Instance.SetDBSetting("AllGroceries", "");
        daysStack.IsVisible = true;
        ApprovedMeals = "";
        App.IsMealReponseLoaded = false;
        NextArrow.IsVisible = false;
        PreviousArrow.IsVisible = false;
        App.IsMealPlanLoading = true;
        index = 0;

        try
        {
            var planexisting = LocalDBManager.Instance.GetDBSetting("Plan")?.Value;
            if (!string.IsNullOrEmpty(planexisting) && currentDay != 1)
            {
                App.MealTtypes = LocalDBManager.Instance.GetDBSetting("MealTtypes")?.Value;
                App.plan = JsonConvert.DeserializeObject<DmmMealPlan>(planexisting);
                App.plan.DaysOnPlan = currentDay;
                LocalDBManager.Instance.SetDBSetting("Plan", JsonConvert.SerializeObject(App.plan));
            }
            else
            {
                App.plan = new DmmMealPlan()
                {
                    Id = Guid.NewGuid(),
                    IsAnyAllergies = App.IsAnyAllergies,
                    Allergies = App.AllergyText,
                    IsAnyFoodYouDontLike = App.IsAnyFoodYouDontLike,
                    AllFoodsYouDontLikes = App.FoodsYouDontLikeText,
                    Country = App.CountryText,
                    FavoriteDiet = App.FavouriteFood,
                    VegetarianOptions = App.VegetarianEats,
                    DaysOnPlan = currentDay
                };
                LocalDBManager.Instance.SetDBSetting("Plan", JsonConvert.SerializeObject(App.plan));
                LocalDBManager.Instance.SetDBSetting("MealTtypes", App.MealTtypes);
            }

            //decimal totalProteinGrams = BreakfastProteinGrams + LunchProteinGrams + DinnerProteinGrams + Snack1ProteinGrams + Snack2ProteinGrams + ProteinShakeProteinGrams;
            double totalProteinGrams =0, totalCarbsGrams =0, totalFatsGrams = 0;
            try
            {
                totalProteinGrams = (App.minPro + App.maxPro) / 2.0;
                roundedProsValue = Math.Round(totalProteinGrams).ToString();
                // roundedProsValue = (totalProteinGrams % 1 == 0) ? totalProteinGrams.ToString("0") : Math.Round((decimal)totalProteinGrams, 1).ToString();
                //decimal totalCarbsGrams = BreakfastCarbsGrams + LunchCarbsGrams + DinnerCarbsGrams + Snack1CarbsGrams + Snack2CarbsGrams + ProteinShakeCarbsGrams;
                totalCarbsGrams = (App.minCarb + App.maxCarb) / 2.0;
                roundedCarbsValue = Math.Round(totalCarbsGrams).ToString();
                // roundedCarbsValue = (totalCarbsGrams % 1 == 0) ? totalCarbsGrams.ToString("0") : Math.Round((decimal)totalCarbsGrams, 1).ToString();
                //decimal totalFatsGrams = BreakfastFatGrams + LunchFatGrams + DinnerFatGrams + Snack1FatGrams + Snack2FatGrams + ProteinShakeFatGrams;
                totalFatsGrams = (App.minFat + App.maxFat) / 2.0;
                roundedFatsValue = Math.Round(totalFatsGrams).ToString();
            }
            catch (Exception ex)
            {
            }
            if ((totalProteinGrams == 0 && totalCarbsGrams == 0 && totalFatsGrams == 0) || string.IsNullOrEmpty(App.MealTtypes))
            {

                //App.IsResetPlan = true;
                var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
                LocalDBManager.Instance.SetDBSetting("FinalMealPlanDay1","");
                Preferences.Set($"IsMealPlanLoading{email}", false);
                OnBeforeShow();
                return;
            }
            // roundedFatsValue = (totalFatsGrams % 1 == 0) ? totalFatsGrams.ToString("0") : Math.Round((decimal)totalFatsGrams, 1).ToString();
            string totalMacros = roundedProsValue + "," + roundedCarbsValue + "," + roundedFatsValue;
            LocalDBManager.Instance.SetDBSetting("totalMacros", totalMacros);
            App.IsMealReponseLoaded = false;

            var modalPage = new Views.GeneralPopup("lamp.png", "", "", "");
            modalPage = new Views.GeneralPopup("medal.png", $"", $"", "Review tips", null, false, false, "false", "false", "false", "false", "true", "false", "false", false, true, totalMacros);

            //var modalPage = new Views.GeneralPopup("lamp.png", "", "", "");
            //modalPage = new Views.GeneralPopup("medal.png", $"Success!", $"Your meal plan is coming right up. With {roundedProsValue} g of protein, you'll gain muscle and burn fat faster.", "Continue", null, false, false, "false", "false", "false", "false", "true", "false", "false", false, true);


            Config.ShowTipsNumber += 1;
            if (modalPage != null)
            {
                await PopupNavigation.Instance.PushAsync(modalPage);
            }
            else
            {
                DisplayAlert("Error", "The Page is not accessible.Try again", "Ok");
                return;
            }

            //StringBuilder totalMacros = new StringBuilder();
            //totalMacros.AppendLine($"{((_targetIntake % 1 == 0) ? _targetIntake.ToString("0") : Math.Round((decimal)_targetIntake, 1).ToString())} calories:");
            //totalMacros.AppendLine($"{roundedProsValue} g protein, {roundedCarbsValue} g carbs, {roundedFatsValue} g fat");
            //await AddMealPlan(totalMacros.ToString());
            resetClicked = false;
            App.BotList.Clear();
            string FavouriteDiet = LocalDBManager.Instance.GetDBSetting("FavoriteDiet")?.Value;
            string data = $"Final {FavouriteDiet} meal plan:";
            await AddMealPlan(data, true, true, false, false, false, null);

            var array = App.MealTtypes.Split(',');
            if (BreakLoop == true)
            {
                //await Task.Delay(3000);
            }
            BreakLoop = false;
            for (var i = 0; i < array?.Length; i++)
            {
                if (resetClicked)
                    break;
                var mealType = array?[i].TrimStart().ToLower(); // Ignore case
                string response = "";
                if (resetClicked || BreakLoop)
                {
                    BFMealHeaderForGPT3 = "";
                    ApprovedMeals = "";
                    return;
                }
                
                switch (mealType)
                {
                    case "breakfast":
                        var exitingBreakfasts = "";
                        if (checkForExistingFoods)
                        {
                            for (int h = 1; h <= 7; h++)
                            {
                                var MealPlanDay = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + h)?.Value;
                                if (!string.IsNullOrEmpty(MealPlanDay))
                                {
                                    string[] sections = MealPlanDay.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                                    if (sections?.Length > 0 && !string.IsNullOrEmpty(sections[0]))
                                    {
                                        exitingBreakfasts += sections[0];
                                        exitingBreakfasts += "\n,\n";
                                    }
                                }
                            }
                        }
                        response = await ProcessMealAsync(App.plan, App.BreakfastMinProteinGrams, App.BreakfastMinCarbsGrams, App.BreakfastMinFatGrams, App.BreakfastMinCalories, App.BreakfastMaxProteinGrams, App.BreakfastMaxCarbsGrams, App.BreakfastMaxFatGrams, App.BreakfastMaxCalories, "Breakfast", 0, exitingBreakfasts);
                        BFMealHeaderForGPT3 = await GetHeaderForReceipe(response);
                        ApprovedMeals += response;
                        ApprovedMeals += "\r\n$";
                        ApprovedMeals += "\r\n";
                        break;
                    case "lunch":
                        var exitingLunch = "";
                        if (checkForExistingFoods)
                        {
                            for (int h = 1; h <= 7; h++)
                            {
                                var MealPlanDay = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + h)?.Value;
                                if (!string.IsNullOrEmpty(MealPlanDay))
                                {
                                    string[] sections = MealPlanDay.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                                    if (sections?.Length > 0 && !string.IsNullOrEmpty(sections[1]))
                                    {
                                        exitingLunch += sections[1];
                                        exitingLunch += "\n,\n";
                                    }
                                }
                            }
                        }
                        response = await ProcessMealAsync(App.plan, App.LunchMinProteinGrams, App.LunchMinCarbsGrams, App.LunchMinFatGrams, App.LunchMinCalories, App.LunchMaxProteinGrams, App.LunchMaxCarbsGrams, App.LunchMaxFatGrams, App.LunchMaxCalories, "Lunch", 0, exitingLunch, ApprovedMeals);
                        LunchMealHeaderForGPT3 = await GetHeaderForReceipe(response);
                        ApprovedMeals += response;
                        ApprovedMeals += "\r\n$";
                        ApprovedMeals += "\r\n";
                        break;
                    case "dinner":
                        var exitingDinner = "";
                        if (checkForExistingFoods)
                        {
                            for (int h = 1; h <= 7; h++)
                            {
                                var MealPlanDay = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + h)?.Value;
                                if (!string.IsNullOrEmpty(MealPlanDay))
                                {
                                    string[] sections = MealPlanDay.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                                    if (sections?.Length > 0 && !string.IsNullOrEmpty(sections[2]))
                                    {
                                        exitingDinner += sections[2];
                                        exitingDinner += "\n,\n";
                                    }
                                }
                            }
                        }
                        response = await ProcessMealAsync(App.plan, App.DinnerMinProteinGrams, App.DinnerMinCarbsGrams, App.DinnerMinFatGrams, App.DinnerMinCalories, App.DinnerMaxProteinGrams, App.DinnerMaxCarbsGrams, App.DinnerMaxFatGrams, App.DinnerMaxCalories, "Dinner", 0, exitingDinner, ApprovedMeals);
                        DinnerMealHeaderForGPT3 = await GetHeaderForReceipe(response);
                        ApprovedMeals += response;
                        ApprovedMeals += "\r\n$";
                        ApprovedMeals += "\r\n";
                        break;
                    case "snack 1":
                        var exitingSnack1 = "";
                        if (checkForExistingFoods)
                        {
                            for (int h = 1; h <= 7; h++)
                            {
                                var MealPlanDay = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + h)?.Value;
                                if (!string.IsNullOrEmpty(MealPlanDay))
                                {
                                    string[] sections = MealPlanDay.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                                    if (sections?.Length > 0 && !string.IsNullOrEmpty(sections[3]))
                                    {
                                        exitingSnack1 += sections[3];
                                        exitingSnack1 += "\n,\n";
                                    }
                                }
                            }
                        }
                        response = await ProcessMealAsync(App.plan, App.Snack1MinProteinGrams, App.Snack1MinCarbsGrams, App.Snack1MinFatGrams, App.Snack1MinCalories, App.Snack1MaxProteinGrams, App.Snack1MaxCarbsGrams, App.Snack1MaxFatGrams, App.Snack1MaxCalories, "Snack 1", 0, exitingSnack1, ApprovedMeals);
                        S1MealHeaderForGPT3 = await GetHeaderForReceipe(response);
                        ApprovedMeals += response;
                        ApprovedMeals += "$";
                        break;
                    case "snack 2":
                        var exitingSnack2 = "";
                        if (checkForExistingFoods)
                        {
                            for (int h = 1; h <= 7; h++)
                            {
                                var MealPlanDay = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + h)?.Value;
                                if (!string.IsNullOrEmpty(MealPlanDay))
                                {
                                    string[] sections = MealPlanDay.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                                    if (sections?.Length > 0 && !string.IsNullOrEmpty(sections[4]))
                                    {
                                        exitingSnack2 += sections[4];
                                        exitingSnack2 += "\n,\n";
                                    }
                                }
                            }
                        }
                        response = await ProcessMealAsync(App.plan, App.Snack2MinProteinGrams, App.Snack2MinCarbsGrams, App.Snack2MinFatGrams, App.Snack2MinCalories, App.Snack2MaxProteinGrams, App.Snack2MaxCarbsGrams, App.Snack2MaxFatGrams, App.Snack2MaxCalories, "Snack 2", 0, exitingSnack2, ApprovedMeals);
                        S2MealHeaderForGPT3 = await GetHeaderForReceipe(response);
                        ApprovedMeals += response;
                        ApprovedMeals += "$";
                        break;
                    case "protein shake":
                        response = await GetProteinShakeReceipe();
                        ApprovedMeals += response;
                        ApprovedMeals += "$";
                        //response = await ProcessMealAsync(plan, ProteinShakeProteinGrams, ProteinShakeCarbsGrams, ProteinShakeFatGrams, ProteinShakeCalories, "Protein shake", 0);
                        break;
                }
                if (resetClicked || BreakLoop)
                {
                    BFMealHeaderForGPT3 = "";
                    ApprovedMeals = "";
                    return;
                }
                await AddMealPlan(response, true, false, false, false, true, true);
            }
            App.IsMealReponseLoaded = true;

            IsFirstMealChange = false;
            isSecondMealChange = false;
            isThirdMealChange = false;
            isForthMealChange = false;
            isFifthMealChange = false;
            isSixthMealChange = false;

            await GetResultFromGPT4();
            //ContinueToChat("");


            //var mealType = array?[0].TrimStart().ToLower(); // Ignore case
            //string response = "";
            //switch (mealType)
            //{
            //    case "breakfast":
            //        response = await ProcessMealAsync(plan, BreakfastMinProteinGrams, BreakfastMinCarbsGrams, BreakfastMinFatGrams, BreakfastMinCalories,BreakfastMaxProteinGrams, BreakfastMaxCarbsGrams, BreakfastMaxFatGrams, BreakfastMaxCalories, "Breakfast", 0);
            //        BFMealHeaderForGPT3 = await GetHeaderForReceipe(response);
            //        break;
            //    case "lunch":
            //        response = await ProcessMealAsync(plan, LunchMinProteinGrams, LunchMinCarbsGrams, LunchMinFatGrams, LunchMinCalories,LunchMaxProteinGrams, LunchMaxCarbsGrams, LunchMaxFatGrams, LunchMaxCalories, "Lunch", 0);
            //        LunchMealHeaderForGPT3 = await GetHeaderForReceipe(response);
            //        break;
            //    case "dinner":
            //        response = await ProcessMealAsync(plan, DinnerMinProteinGrams, DinnerMinCarbsGrams, DinnerMinFatGrams, DinnerMinCalories, DinnerMaxProteinGrams, DinnerMaxCarbsGrams, DinnerMaxFatGrams, DinnerMaxCalories, "Dinner", 0);
            //        DinnerMealHeaderForGPT3 = await GetHeaderForReceipe(response);
            //        break;
            //    case "snack 1":
            //        response = await ProcessMealAsync(plan, Snack1MinProteinGrams, Snack1MinCarbsGrams, Snack1MinFatGrams, Snack1MinCalories, Snack1MaxProteinGrams, Snack1MaxCarbsGrams, Snack1MaxFatGrams, Snack1MaxCalories, "Snack 1", 0);
            //        S1MealHeaderForGPT3 = await GetHeaderForReceipe(response);
            //        break;
            //    case "snack 2":
            //        response = await ProcessMealAsync(plan, Snack2MinProteinGrams, Snack2MinCarbsGrams, Snack2MinFatGrams, Snack2MinCalories, Snack2MaxProteinGrams, Snack2MaxCarbsGrams, Snack2MaxFatGrams, Snack2MaxCalories, "Snack 2", 0);
            //        S2MealHeaderForGPT3 = await GetHeaderForReceipe(response);
            //        break;
            //    case "protein shake":
            //        response = await GetProteinShakeReceipe();
            //        //response = await ProcessMealAsync(plan, ProteinShakeProteinGrams, ProteinShakeCarbsGrams, ProteinShakeFatGrams, ProteinShakeCalories, "Protein shake", 0);
            //        break;
            //}
            //await AddMealPlan(response);
            //ContinueToChat(firstMealResponse);


        }
        catch (Exception ex)
        {
            //UserDialogs.Instance.HideLoading();
        }


    }
    private async void ReviseMealPlan()
    {
        try
        {
            // Part 1

            if (LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value != null)
            {
                _targetIntake = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("TargetIntake").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
            }
            _targetIntake = Math.Round(_targetIntake);

            var fav = LocalDBManager.Instance.GetDBSetting("FavoriteDiet")?.Value;
            App.FavouriteFood = fav;

            if (App.FavouriteFood == "Mediterranean")
            {
                // 15-20% pro, 45-55% carb, 30-35% fat
                //  60-70% carb, 30-40% fat
                await CalculateAllMacrosByDietType(0.15m, 0.20m, 0.60m, 0.70m, 0.30m, 0.40m);
            }
            else if (App.FavouriteFood == "Vegetarian")
            {
                // 20-30% pro, 40-50% carb, 30-35% fat
                // // 55-65% carb, 35-45% fat
                await CalculateAllMacrosByDietType(0.20m, 0.30m, 0.55m, 0.65m, 0.35m, 0.45m);
            }
            else if (App.FavouriteFood == "Vegan")
            {
                // 12-22% pro, 50-75% carb, 12-17% fat
                // 18-25% pro, 50-75% carb, 12-17% fat
                // 65-75% carb, 25-35% fat
                await CalculateAllMacrosByDietType(0.18m, 0.25m, 0.65m, 0.75m, 0.25m, 0.35m);
            }
            else if (App.FavouriteFood == "Paleo")
            {
                // 27-33% pro, 25-37% carb, 32-42% fat
                // 35-45% carb, 55-65% fat
                await CalculateAllMacrosByDietType(0.27m, 0.33m, 0.35m, 0.45m, 0.55m, 0.65m);
            }
            else if (App.FavouriteFood == "Keto")
            {
                // 20-33% pro, 5-15% carb, 60-73% fat
                // 20-30% carb, 70-80% fat
                await CalculateAllMacrosByDietType(0.20m, 0.33m, 0.20m, 0.30m, 0.70m, 0.80m);
            }
            else
            {
                // Protein ranges
                MatchCollection matches = Regex.Matches(ProteinRecom, @"\d+");
                MatchCollection matches1 = Regex.Matches(CarbRecom, @"\d+");
                MatchCollection matches2 = Regex.Matches(FatRecom, @"\d+");
                if (matches.Count >= 2)
                {
                    App.minPro = int.Parse(matches[0].Value);
                    App.maxPro = int.Parse(matches[1].Value);
                }
                // Carb ranges
                if (matches.Count >= 2)
                {
                    App.minCarb = int.Parse(matches1[0].Value);
                    App.maxCarb = int.Parse(matches1[1].Value);
                }
                // Protein ranges
                if (matches.Count >= 2)
                {
                    App.minFat = int.Parse(matches2[0].Value);
                    App.maxFat = int.Parse(matches2[1].Value);
                }
            }

            await CalculateMacros(_targetIntake, App.minPro, App.maxPro, App.minCarb, App.maxCarb, App.minFat, App.maxFat);

            // Part 2

            //await CalculateAllMacros();

            //_targetIntake = Math.Round(_targetIntake);
            
            IsDefaultMeals = true;

            if (IsDefaultMeals)
            {
                App.ProteinShakeProteinGrams = 30;
                calculatedProteins = calculatedProteins - App.ProteinShakeProteinGrams;
                calculatedCarbs = calculatedCarbs - App.ProteinShakeCarbsGrams;
                calculatedFats = calculatedFats - App.ProteinShakeFatGrams;
                App.BreakfastMinProteinGrams = (App.minPro - 5) * 40 / 100;
                App.BreakfastMaxProteinGrams = (App.maxPro - 5) * 40 / 100;
                App.BreakfastMinCarbsGrams = App.minCarb * 30 / 100;
                App.BreakfastMaxCarbsGrams = App.maxCarb * 30 / 100;
                App.BreakfastMinFatGrams = App.minFat * 30 / 100;
                App.BreakfastMaxFatGrams = App.maxFat * 30 / 100;
                App.BreakfastMinCalories = (App.BreakfastMinProteinGrams * 4) + (App.BreakfastMinCarbsGrams * 4) + (App.BreakfastMinFatGrams * 9);
                App.BreakfastMaxCalories = (App.BreakfastMaxProteinGrams * 4) + (App.BreakfastMaxCarbsGrams * 4) + (App.BreakfastMaxFatGrams * 9);

                App.LunchMinProteinGrams = (App.minPro - 5) * 30 / 100;
                App.LunchMaxProteinGrams = (App.maxPro - 5) * 30 / 100;
                App.LunchMinCarbsGrams = App.minCarb * 35 / 100;
                App.LunchMaxCarbsGrams = App.maxCarb * 35 / 100;
                App.LunchMinFatGrams = App.minFat * 25 / 100;
                App.LunchMaxFatGrams = App.maxFat * 25 / 100;
                App.LunchMinCalories = (App.LunchMinProteinGrams * 4) + (App.LunchMinCarbsGrams * 4) + (App.LunchMinFatGrams * 9);
                App.LunchMaxCalories = (App.LunchMaxProteinGrams * 4) + (App.LunchMaxCarbsGrams * 4) + (App.LunchMaxFatGrams * 9);

                App.DinnerMinProteinGrams = (App.minPro - 5) * 30 / 100;
                App.DinnerMaxProteinGrams = (App.maxPro - 5) * 30 / 100;
                App.DinnerMinCarbsGrams = App.minCarb * 35 / 100;
                App.DinnerMaxCarbsGrams = App.maxCarb * 35 / 100;
                App.DinnerMinFatGrams = App.minFat * 45 / 100;
                App.DinnerMaxFatGrams = App.maxFat * 45 / 100;
                App.DinnerMinCalories = (App.DinnerMinProteinGrams * 4) + (App.DinnerMinCarbsGrams * 4) + (App.DinnerMinFatGrams * 9);
                App.DinnerMaxCalories = (App.DinnerMaxProteinGrams * 4) + (App.DinnerMaxCarbsGrams * 4) + (App.DinnerMaxFatGrams * 9);


                App.Snack1MinProteinGrams = 0;
                App.Snack1MaxProteinGrams = 0;
                App.Snack1MinCarbsGrams = 0;
                App.Snack1MaxCarbsGrams = 0;
                App.Snack1MinFatGrams = 0;
                App.Snack1MaxFatGrams = 0;
                App.Snack1MinCalories = 0;
                App.Snack1MaxCalories = 0;

                App.Snack2MinProteinGrams = 0;
                App.Snack2MaxProteinGrams = 0;
                App.Snack2MinCarbsGrams = 0;
                App.Snack2MaxCarbsGrams = 0;
                App.Snack2MinFatGrams = 0;
                App.Snack2MaxFatGrams = 0;
                App.Snack2MinCalories = 0;
                App.Snack2MaxCalories = 0;

                //ProteinShakeProteinGrams = calculatedProteins * 10 / 100;
                App.ProteinShakeCalories = (App.ProteinShakeProteinGrams * 4) + (App.ProteinShakeCarbsGrams * 4) + (App.ProteinShakeFatGrams * 9);
            }
            else
            {
                App.ProteinShakeProteinGrams = IsProteinShake ? 30 : 0;
                App.ProteinShakeCarbsGrams = IsProteinShake ? 12 : 0;
                App.ProteinShakeFatGrams = IsProteinShake ? 4 : 0;

                calculatedProteins = calculatedProteins - App.ProteinShakeProteinGrams;
                calculatedCarbs = calculatedCarbs - App.ProteinShakeCarbsGrams;
                calculatedFats = calculatedFats - App.ProteinShakeFatGrams;
                var array = App.MealTtypes.Split(',');
                if (array?.Length > 0)
                {
                    decimal proteinPercentageValue = 0;
                    decimal carbsPercentageValue = 0;
                    decimal fatPercentageValue = 0;
                    decimal snackValue = 0;
                    if ((array?.Length > 2 && IsSnack1 && IsSnack2 && !IsProteinShake) || (array?.Length > 3 && IsSnack1 && IsSnack2 && IsProteinShake))
                    {
                        snackValue = 10;
                        proteinPercentageValue = 80 / ((IsProteinShake) ? array.Length - 3 : array.Length - 2);
                        carbsPercentageValue = proteinPercentageValue;
                        fatPercentageValue = proteinPercentageValue;
                    }
                    else if ((array?.Length > 2 && IsSnack1) || (array?.Length > 2 && IsSnack2))
                    {
                        snackValue = 20;
                        proteinPercentageValue = 80 / ((IsProteinShake) ? array.Length - 2 : array.Length - 1);
                        carbsPercentageValue = proteinPercentageValue;
                        fatPercentageValue = proteinPercentageValue;
                    }
                    else
                    {
                        proteinPercentageValue = CalculateRatioByLength((IsProteinShake) ? array.Length - 1 : array.Length);
                        carbsPercentageValue = proteinPercentageValue;
                        fatPercentageValue = proteinPercentageValue;
                    }


                    for (var i = 0; i <= array.Length; i++)
                    {
                        if (IsBreakfast)
                        {
                            CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.BreakfastMinProteinGrams, out App.BreakfastMinCarbsGrams, out App.BreakfastMinFatGrams, out App.BreakfastMinCalories, out App.BreakfastMaxProteinGrams, out App.BreakfastMaxCarbsGrams, out App.BreakfastMaxFatGrams, out App.BreakfastMaxCalories);
                        }
                        if (IsLunch)
                        {
                            CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.LunchMinProteinGrams, out App.LunchMinCarbsGrams, out App.LunchMinFatGrams, out App.LunchMinCalories, out App.LunchMaxProteinGrams, out App.LunchMaxCarbsGrams, out App.LunchMaxFatGrams, out App.LunchMaxCalories);
                        }
                        if (IsDinner)
                        {
                            CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.DinnerMinProteinGrams, out App.DinnerMinCarbsGrams, out App.DinnerMinFatGrams, out App.DinnerMinCalories, out App.DinnerMaxProteinGrams, out App.DinnerMaxCarbsGrams, out App.DinnerMaxFatGrams, out App.DinnerMaxCalories);
                        }
                        if (IsSnack1)
                        {
                            if (snackValue > 0)
                                CalculateMealNutrients(snackValue, snackValue, snackValue, out App.Snack1MinProteinGrams, out App.Snack1MinCarbsGrams, out App.Snack1MinFatGrams, out App.Snack1MinCalories, out App.Snack1MaxProteinGrams, out App.Snack1MaxCarbsGrams, out App.Snack1MaxFatGrams, out App.Snack1MaxCalories);
                            else
                                CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.Snack1MinProteinGrams, out App.Snack1MinCarbsGrams, out App.Snack1MinFatGrams, out App.Snack1MinCalories, out App.Snack1MaxProteinGrams, out App.Snack1MaxCarbsGrams, out App.Snack1MaxFatGrams, out App.Snack1MaxCalories);
                        }
                        if (IsSnack2)
                        {
                            if (snackValue > 0)
                                CalculateMealNutrients(snackValue, snackValue, snackValue, out App.Snack2MinProteinGrams, out App.Snack2MinCarbsGrams, out App.Snack2MinFatGrams, out App.Snack2MinCalories, out App.Snack2MaxProteinGrams, out App.Snack2MaxCarbsGrams, out App.Snack2MaxFatGrams, out App.Snack2MaxCalories);
                            else
                                CalculateMealNutrients(proteinPercentageValue, carbsPercentageValue, fatPercentageValue, out App.Snack2MinProteinGrams, out App.Snack2MinCarbsGrams, out App.Snack2MinFatGrams, out App.Snack2MinCalories, out App.Snack2MaxProteinGrams, out App.Snack2MaxCarbsGrams, out App.Snack2MaxFatGrams, out App.Snack2MaxCalories);
                        }
                        if (IsProteinShake)
                        {
                            App.ProteinShakeCalories = (App.ProteinShakeProteinGrams * 4) + (App.ProteinShakeCarbsGrams * 4) + (App.ProteinShakeFatGrams * 9);
                        }
                    }
                }

            }

            //SetMacrosLocally();

            MacrosDistribution macrosDistribution = new MacrosDistribution();
            macrosDistribution.TargetIntake = _targetIntake;
            macrosDistribution.MinPro = App.minPro;
            macrosDistribution.MaxPro = App.maxPro;
            macrosDistribution.MinCarb = App.minCarb;
            macrosDistribution.MaxCarb = App.maxCarb;
            macrosDistribution.MinFat = App.minFat;
            macrosDistribution.MaxFat = App.maxFat;

            macrosDistribution.BreakfastMinCalories = App.BreakfastMinCalories;
            macrosDistribution.BreakfastMaxCalories = App.BreakfastMaxCalories;
            macrosDistribution.BreakfastMinProteinGrams = App.BreakfastMinProteinGrams;
            macrosDistribution.BreakfastMaxProteinGrams = App.BreakfastMaxProteinGrams;
            macrosDistribution.BreakfastMinCarbsGrams = App.BreakfastMinCarbsGrams;
            macrosDistribution.BreakfastMaxCarbsGrams = App.BreakfastMaxCarbsGrams;
            macrosDistribution.BreakfastMinFatGrams = App.BreakfastMinFatGrams;
            macrosDistribution.BreakfastMaxFatGrams = App.BreakfastMaxFatGrams;

            macrosDistribution.LunchMinCalories = App.LunchMinCalories;
            macrosDistribution.LunchMaxCalories = App.LunchMaxCalories;
            macrosDistribution.LunchMinProteinGrams = App.LunchMinProteinGrams;
            macrosDistribution.LunchMaxProteinGrams = App.LunchMaxProteinGrams;
            macrosDistribution.LunchMinCarbsGrams = App.LunchMinCarbsGrams;
            macrosDistribution.LunchMaxCarbsGrams = App.LunchMaxCarbsGrams;
            macrosDistribution.LunchMinFatGrams = App.LunchMinFatGrams;
            macrosDistribution.LunchMaxFatGrams = App.LunchMaxFatGrams;

            macrosDistribution.DinnerMinCalories = App.DinnerMinCalories;
            macrosDistribution.DinnerMaxCalories = App.DinnerMaxCalories;
            macrosDistribution.DinnerMinProteinGrams = App.DinnerMinProteinGrams;
            macrosDistribution.DinnerMaxProteinGrams = App.DinnerMaxProteinGrams;
            macrosDistribution.DinnerMinCarbsGrams = App.DinnerMinCarbsGrams;
            macrosDistribution.DinnerMaxCarbsGrams = App.DinnerMaxCarbsGrams;
            macrosDistribution.DinnerMinFatGrams = App.DinnerMinFatGrams;
            macrosDistribution.DinnerMaxFatGrams = App.DinnerMaxFatGrams;

            macrosDistribution.ProteinShakeCalories = App.ProteinShakeCalories;
            macrosDistribution.ProteinShakeProteinGrams = App.ProteinShakeProteinGrams;
            macrosDistribution.ProteinShakeCarbsGrams = App.ProteinShakeCarbsGrams;
            macrosDistribution.ProteinShakeFatGrams = App.ProteinShakeFatGrams;

            macrosDistribution.Snack1MinCalories = App.Snack1MinCalories;
            macrosDistribution.Snack1MaxCalories = App.Snack1MaxCalories;
            macrosDistribution.Snack1MinProteinGrams = App.Snack1MinProteinGrams;
            macrosDistribution.Snack1MaxProteinGrams = App.Snack1MaxProteinGrams;
            macrosDistribution.Snack1MinCarbsGrams = App.Snack1MinCarbsGrams;
            macrosDistribution.Snack1MaxCarbsGrams = App.Snack1MaxCarbsGrams;
            macrosDistribution.Snack1MinFatGrams = App.Snack1MinFatGrams;
            macrosDistribution.Snack1MaxFatGrams = App.Snack1MaxFatGrams;

            macrosDistribution.Snack2MinCalories = App.Snack2MinCalories;
            macrosDistribution.Snack2MaxCalories = App.Snack2MaxCalories;
            macrosDistribution.Snack2MinProteinGrams = App.Snack2MinProteinGrams;
            macrosDistribution.Snack2MaxProteinGrams = App.Snack2MaxProteinGrams;
            macrosDistribution.Snack2MinCarbsGrams = App.Snack2MinCarbsGrams;
            macrosDistribution.Snack2MaxCarbsGrams = App.Snack2MaxCarbsGrams;
            macrosDistribution.Snack2MinFatGrams = App.Snack2MinFatGrams;
            macrosDistribution.Snack2MaxFatGrams = App.Snack2MaxFatGrams;

            LocalDBManager.Instance.SetDBSetting("MacrosDistribution", JsonConvert.SerializeObject(macrosDistribution));

            await CalculateMacros(macrosDistribution.TargetIntake, macrosDistribution.MinPro, macrosDistribution.MaxPro, 
                                    macrosDistribution.MinCarb, macrosDistribution.MaxCarb, macrosDistribution.MinFat, macrosDistribution.MaxFat);
            MainGrid.IsVisible = true;
            GenerateBtnsStack.IsVisible = false;

            SyncWithServer();

            // Part 3
            //var macrosDistribution1 = LocalDBManager.Instance.GetDBSetting("MacrosDistribution")?.Value;

            //if (!string.IsNullOrEmpty(macrosDistribution1))
            //{
            //    var obj = JsonConvert.DeserializeObject<MacrosDistribution>(macrosDistribution1);
            //    await CalculateMacros(obj.TargetIntake, obj.MinPro, obj.MaxPro, obj.MinCarb, obj.MaxCarb, obj.MinFat, obj.MaxFat);
            //    MainGrid.IsVisible = true;
            //    GenerateBtnsStack.IsVisible = false;
            //    App.minPro = obj.MinPro;
            //    App.maxPro = obj.MaxPro;
            //    App.minCarb = obj.MinCarb;
            //    App.maxCarb = obj.MaxCarb;
            //    App.minFat = obj.MinFat;
            //    App.maxFat = obj.MaxFat;

            //    App.BreakfastMinCalories = obj.BreakfastMinCalories;
            //    App.BreakfastMaxCalories = obj.BreakfastMaxCalories;
            //    App.BreakfastMinProteinGrams = obj.BreakfastMinProteinGrams;
            //    App.BreakfastMaxProteinGrams = obj.BreakfastMaxProteinGrams;
            //    App.BreakfastMinCarbsGrams = obj.BreakfastMinCarbsGrams;
            //    App.BreakfastMaxCarbsGrams = obj.BreakfastMaxCarbsGrams;
            //    App.BreakfastMinFatGrams = obj.BreakfastMinFatGrams;
            //    App.BreakfastMaxFatGrams = obj.BreakfastMaxFatGrams;

            //    App.LunchMinCalories = obj.LunchMinCalories;
            //    App.LunchMaxCalories = obj.LunchMaxCalories;
            //    App.LunchMinProteinGrams = obj.LunchMinProteinGrams;
            //    App.LunchMaxProteinGrams = obj.LunchMaxProteinGrams;
            //    App.LunchMinCarbsGrams = obj.LunchMinCarbsGrams;
            //    App.LunchMaxCarbsGrams = obj.LunchMaxCarbsGrams;
            //    App.LunchMinFatGrams = obj.LunchMinFatGrams;
            //    App.LunchMaxFatGrams = obj.LunchMaxFatGrams;

            //    App.DinnerMinCalories = obj.DinnerMinCalories;
            //    App.DinnerMaxCalories = obj.DinnerMaxCalories;
            //    App.DinnerMinProteinGrams = obj.DinnerMinProteinGrams;
            //    App.DinnerMaxProteinGrams = obj.DinnerMaxProteinGrams;
            //    App.DinnerMinCarbsGrams = obj.DinnerMinCarbsGrams;
            //    App.DinnerMaxCarbsGrams = obj.DinnerMaxCarbsGrams;
            //    App.DinnerMinFatGrams = obj.DinnerMinFatGrams;
            //    App.DinnerMaxFatGrams = obj.DinnerMaxFatGrams;

            //    App.ProteinShakeCalories = obj.ProteinShakeCalories;
            //    App.ProteinShakeProteinGrams = obj.ProteinShakeProteinGrams;
            //    App.ProteinShakeCarbsGrams = obj.ProteinShakeCarbsGrams;
            //    App.ProteinShakeFatGrams = obj.ProteinShakeFatGrams;

            //    App.Snack1MinCalories = obj.Snack1MinCalories;
            //    App.Snack1MaxCalories = obj.Snack1MaxCalories;
            //    App.Snack1MinProteinGrams = obj.Snack1MinProteinGrams;
            //    App.Snack1MaxProteinGrams = obj.Snack1MaxProteinGrams;
            //    App.Snack1MinCarbsGrams = obj.Snack1MinCarbsGrams;
            //    App.Snack1MaxCarbsGrams = obj.Snack1MaxCarbsGrams;
            //    App.Snack1MinFatGrams = obj.Snack1MinFatGrams;
            //    App.Snack1MaxFatGrams = obj.Snack1MaxFatGrams;

            //    App.Snack2MinCalories = obj.Snack2MinCalories;
            //    App.Snack2MaxCalories = obj.Snack2MaxCalories;
            //    App.Snack2MinProteinGrams = obj.Snack2MinProteinGrams;
            //    App.Snack2MaxProteinGrams = obj.Snack2MaxProteinGrams;
            //    App.Snack2MinCarbsGrams = obj.Snack2MinCarbsGrams;
            //    App.Snack2MaxCarbsGrams = obj.Snack2MaxCarbsGrams;
            //    App.Snack2MinFatGrams = obj.Snack2MinFatGrams;
            //    App.Snack2MaxFatGrams = obj.Snack2MaxFatGrams;
            //    SyncWithServer();
            //}
        }
        catch (Exception ex)
        {

        }
    }
    private async Task<string> GetHeaderForReceipe(string input)
    {
        try
        {
            string firstLineWithColon = input.Split('\n')
                                             .FirstOrDefault(line => line.Contains(":"))
                                             ?.Trim();
            return firstLineWithColon;
        }
        catch (Exception ex)
        {
            return "";
        }
    }
    private async Task<string> GetProteinShakeReceipe()
    {
        StringBuilder proteinShake = new StringBuilder();
        proteinShake.AppendLine($"Protein Shake:");
        proteinShake.AppendLine($"1 scoop (30 g powder)");
        proteinShake.AppendLine($"1 cup any milk of your choice");
        proteinShake.AppendLine($"[Stop here]");
        return proteinShake.ToString();
    }
    static double GetMidValue(double a, double b)
    {
        try
        {
            return (double)(((long)a + (long)b) / 2);
        }
        catch (Exception ex)
        {
            return a;
        }
    }
    private async Task<string> ProcessMealAsync(DmmMealPlan plan, decimal minprotein, decimal mincarbs, decimal minfat, decimal mincalories, decimal maxprotein, decimal maxcarbs, decimal maxfat, decimal maxcalories, string mealType, int indexValue, string assistant = "",string ApprovedMeals = "")
    {
        try
        {
            string mealRequest = await GetMealtInfoRequestGPT(plan, (int)Math.Round(minprotein), (int)Math.Round(mincarbs), (int)Math.Round(minfat), (int)Math.Round(mincalories), (int)Math.Round(maxprotein), (int)Math.Round(maxcarbs), (int)Math.Round(maxfat), (int)Math.Round(maxcalories), mealType.ToString());
            //var gptResponse = await AnaliesAIWithChatGPT(mealRequest,0,2000,0,0,0);
            var gptResponse = await AnaliesAIWithChatGPT4o(mealRequest, 0, 2000, 0, 0, 0, assistant, ApprovedMeals);
            string data = gptResponse.choices.FirstOrDefault()?.message.content;

            if (!string.IsNullOrEmpty(data) && data.Contains("..."))
            {
                data = await ProcessMealAsync(plan, minprotein, mincarbs, minfat, mincalories, maxprotein, maxcarbs, maxfat, maxcalories, mealType, indexValue);
            }

            if (!string.IsNullOrEmpty(data))
            {
                lastResultForGPT3 = data;
            }

            Console.WriteLine(mealType + " = " + data);
            //string data = mealType;
            switch (indexValue)
            {
                case 0:
                    firstMealResponse = data;
                    break;
                case 1:
                    secondMealResponse = data;
                    break;
                case 2:
                    thirdMealResponse = data;
                    break;
                case 3:
                    forthMealResponse = data;
                    break;
                case 4:
                    fifthMealResponse = data;
                    break;
                default:
                    break;
            }
            return data;
        }
        catch (Exception ex)
        {
            return "Some thing went wrong. Please try again later.";
        }

    }
    private async Task<string> ProcessMealWithGPT4(string plan, decimal minprotein, decimal mincarbs, decimal minfat, decimal mincalories, decimal maxprotein, decimal maxcarbs, decimal maxfat, decimal maxcalories, string mealDetails, string MealHeaderForGPT3, int indexValue, bool isLastMeal = false)
    {
        try
        {
            string mealRequest = "";
            if (plan == "protein shake")
            {
                mealRequest = await GetProteinMealtInfoRequestGPT4(plan, Math.Round(minprotein), Math.Round(mincarbs), Math.Round(minfat), Math.Round(mincalories), mealDetails.ToString());
            }
            else
            {
                mealRequest = await GetMealtInfoRequestGPT4(plan, Math.Round(minprotein), Math.Round(mincarbs), Math.Round(minfat), Math.Round(mincalories), Math.Round(maxprotein), Math.Round(maxcarbs), Math.Round(maxfat), Math.Round(maxcalories), mealDetails.ToString(), MealHeaderForGPT3, isLastMeal);
            }
            Console.WriteLine("Query" + mealRequest);
            var gptResponse = await GetChatGPT4(mealRequest);

            string data = gptResponse.choices.FirstOrDefault()?.message.content;
            //if (!string.IsNullOrEmpty(data) && data.Contains("..."))
            //{
            //    gptResponse = await GetChatGPT4(mealRequest);
            //    data = gptResponse.choices.FirstOrDefault()?.message.content;
            //}
            Console.WriteLine("Response ==" + System.Text.Json.JsonSerializer.Serialize(data));

            return data;
        }
        catch (Exception ex)
        {
            return await ProcessMealWithGPT4(plan, minprotein, mincarbs, minfat, mincalories, maxprotein, maxcarbs, maxfat, maxcalories, mealDetails, MealHeaderForGPT3, indexValue);
        }

    }
    private string ConvertToFinalData(string content, string mealType = "")
    {
        content = content.Replace("*", "");
        // Define the regular expression pattern
        try
        {
            try
            {
                content = string.Join(
                                    Environment.NewLine,
                                    content.Split(new[] { Environment.NewLine }, StringSplitOptions.None)
                                    .Where(line => !string.IsNullOrWhiteSpace(line))
                                    .ToArray());
            }
            catch (Exception ex)
            {

            }

            string pattern = @"Final:(.*?)(?=\n\n|$)";

            // Create a Regex object
            Regex regex = new Regex(pattern, RegexOptions.Singleline);

            // Search for the pattern in the data
            Match match = regex.Match(content);

            // Extract the desired text
            if (match.Success)
            {
                string data = match.Groups[1].Value.Trim();
                data.Replace("Within range:", "");
                return data;
            }
            else
            {
                if (mealType == "protein shake")
                    return content;
                else
                    return "";
            }
        }
        catch (Exception ex)
        {
            return "";


        }
    }

    private async Task<string> GetMealtInfoRequestGPT(DmmMealPlan plan, int ProteinMinGrams, int CarbsMinGrams, int FatMinGrams, int MinCalories, int ProteinMaxGrams, int CarbsMaxGrams, int FatMaxGrams, int MaxCalories, string MealType)
    {
        var midCalories = GetMidValue(MinCalories, MaxCalories);
        var midProteins = GetMidValue(ProteinMinGrams, ProteinMaxGrams);
        var midCarbs = GetMidValue(CarbsMinGrams, CarbsMaxGrams);
        var midFats = GetMidValue(FatMinGrams, FatMaxGrams);

        StringBuilder requestAI = new StringBuilder();
        string diet = !string.IsNullOrEmpty(lastResultForGPT3) ? "Diet must be different from this meal:\r\n" + lastResultForGPT3 : "";
        //var user = await DrMuscleRestClient.Instance.GetUserInfoWithoutLoader();
        string countryDetails = $"Please include foods that easily found in country: {plan.Country}";
        requestAI.AppendLine($"You are a world-class diet coach. I am your client. " +
          $"Please use standard values from a reliable nutritional database for ingredients." +
          $" Give me a simple [{plan.FavoriteDiet}] {MealType}." +
          $"{(!string.IsNullOrEmpty(plan.Country) ? countryDetails : "")}");
        requestAI.AppendLine($"\r\n- Allergies to avoid: " + (plan.IsAnyAllergies ? '[' + plan.Allergies + ']' : "[none]"));
        requestAI.AppendLine($"\r\n- Foods to avoid: " + (plan.IsAnyFoodYouDontLike ? '[' + plan.AllFoodsYouDontLikes + ']' : "[none]"));
        requestAI.AppendLine($"- For protein,Be careful I only need [ {plan.VegetarianOptions} ]" +
            $"And exclude following foods: {unselectedSource}" +
            $"{(!string.IsNullOrEmpty(diet) ? diet : "")}" +
            $"\r\n-Nutritional Targets: Try to reach close to [{midCalories} kcal, {midProteins} g protein, {midCarbs} g carbs, {midFats} g fat] , within a margin of ±5%" +
            //$"\r\n-Nutritional Targets: [{MinCalories}-{MaxCalories} kcal, {ProteinMinGrams}-{ProteinMaxGrams} g protein, {CarbsMinGrams}-{CarbsMaxGrams} g carbs, {FatMinGrams}-{FatMaxGrams} g fat]" +
            $"\r\n**Note: Please make sure output nutrition must exactly match the given targets. Check twice for accuracy and always use reliable nutritional database for ingredients (no prose, no blank line, nothing else).**" +
             $"\r\n\r\n**Macros and Calories Calculation**:" +
 $"\r\n    - Make sure Total kcal = (4 × protein in grams) + (4 × carbs in grams) + (9 × fat in grams)" +
 $"\r\n- Ensure calories match the macros, and round all values." +
 $"\r\n" +
            $"\r\n\r\nFormat your response exactly as follow (no prose, no blank line, nothing else):" +
            $"\r\n\r\n{MealType}: [ensure short title, maximum 3 words]\r\n[Name of ingredient 1] (volume measure)" +
            $"\r\n[Name of ingredient 2] (volume measure)\r\n..." +
            $"\r\n[Stop here]");

        return requestAI.ToString();
    }
    private async Task<string> GetMealtInfoRequestGPT4(string plan, decimal ProteinMinGrams, decimal CarbsMinGrams, decimal FatMinGrams, decimal MinCalories, decimal ProteinMaxGrams, decimal CarbsMaxGrams, decimal FatMaxGrams, decimal MaxCalories, string MealDetails, string mealHeaderForGPT3, bool isLastMeal = false)
    {
        try
        {
            var midCalories = Math.Round(GetMidValue((double)MinCalories, (double)MaxCalories));
            var midProteins = Math.Round(GetMidValue((double)ProteinMinGrams, (double)ProteinMaxGrams));
            var midCarbs = Math.Round(GetMidValue((double)CarbsMinGrams, (double)CarbsMaxGrams));
            var midFats = Math.Round(GetMidValue((double)FatMinGrams, (double)FatMaxGrams));
            var newCAlories = midCalories;
            midCalories = midCalories - (midCalories * 0.10);
            ////midProteins = midProteins - (midProteins * 0.15);
            ////midCarbs = midCarbs - (midCarbs * 0.15);
            ////midFats = midFats - (midFats * 0.15);
            //var midProteins = $"{ProteinMinGrams} - {ProteinMaxGrams}";
            //var midCarbs = $"{CarbsMinGrams} - {CarbsMaxGrams}";
            //var midFats = $"{FatMinGrams} - {FatMaxGrams}";
            string calories = $"{Math.Round(midCalories)} - {newCAlories}";
            var array = App.MealTtypes.Split(',');
            string secondLastElement = (array?.Length >= 2) ? array[array.Length - 2] : "";
            string LastElement = array[array.Length - 1];
            if (isLastMeal)
            {
                calories = $"{TotalKcal}";
                if ((midProteins + (double)TotalProteins) > App.maxPro)
                {
                    midProteins = (double)(App.maxPro - TotalProteins);
                    midProteins = midProteins - (midProteins * 0.10);
                }
            }
            else if (secondLastElement.TrimStart().ToLower() == plan && LastElement.TrimStart().ToLower() == "protein shake")
            {
                var remainingCals = TotalKcal - 225;
                calories = $"{remainingCals - 10} - {remainingCals + 10}";
                double totalProtein = (double)(TotalProteins + 30);
                if ((midProteins + totalProtein) > App.maxPro)
                {
                    midProteins = App.maxPro - totalProtein;
                    midProteins = midProteins - (midProteins * 0.10);
                }
                (midProteins, midCarbs, midFats) = AdjustNutrients((int)(remainingCals - 10), (int)(remainingCals + 10), (int)midProteins, (int)midCarbs, (int)midFats);
            }
            string capitalizedPlan = char.ToUpper(plan[0]) + plan.Substring(1);
            string header = (!string.IsNullOrEmpty(mealHeaderForGPT3)) ? mealHeaderForGPT3 : $"{capitalizedPlan}: [Same meal name as above]";
            StringBuilder requestAI = new StringBuilder();

            var data = MealDetails.Replace("[", "");
            data = data.Replace("]", "");
            data = data.Replace("Stop here", "");
            data = data.Replace("stop here", "");

            string[] lines = data?.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

            // Check if there are more than one line
            if (lines?.Length > 1)
            {
                // Join the lines starting from the second line
                string stringWithoutFirstLine = string.Join(Environment.NewLine, lines.Skip(1));

                data = stringWithoutFirstLine;
            }
            requestAI.AppendLine($"You are a world-class diet coach. Adjust the quantities of the following ingredients for " +
                $"{header} to meet these nutritional targets:" +
                $"\r\nNutritional Targets:\r\n" +
                $"\r\n{calories} kcal, {midProteins} g protein, {midCarbs} g carbs, {midFats} g fat." +
                $"\r\n\r\nDo not add new ingredients and also don't add any ingredient with 0 unit." +
                $"\r\n\r\nIngredients:" +
                $"\r\n{data}" +
                $"\r\n\r\nRequirements:\r\nRound all numeric values to the nearest whole number for calories and macros. Adjust quantities to meet the nutritional targets. " +
                $"Ensure the total kcal/calories is the exact sum of the total protein, carbs, and fat." +
                $"\r\n\r\nGuidelines:" +
                $"\r\nEnsure the total kcal equals (4 * protein g) + (4 * carbs g) + (9 * fat g)." +
                $"\r\nCheck if the result meets the targets, especially calories {calories} kcal, {midProteins} g protein, {midCarbs} g carbs, {midFats} g fat. If not, **then compare given targets and output targets and find which is mismatch then adjust again until targets are met.**" +
                $"\r\n\r\nShow Only Final Total Calculations just before 'final' result, remember values should be rounded and also use these values inside 'final' result:" +
                $"\r\nProtein kcal = 4 * protein in g = \r\nCarbs kcal = 4 * carbs in g carbs\r\nFat kcal = 9 * fat in g" +
                $"\r\nTotal kcal = Protein kcal + Carbs kcal + Fat kcal" +
                $"\r\n\r\nOutput Format:" +
                $"\r\nFinal:\r\n{header}\r\nXXX kcal, XX g protein, XX g carbs, XX g fat\r\n[Ingredient 1] (volume measure)\r\n[Ingredient 2] (volume measure)\r\n..." +

                $"\r\n\r\nImportant:" +
                $"\r\nEnsure total calories are within the range of {calories} kcal." +
                $"\r\n10% margin of up/down values for protein {midProteins} g, carbs {midCarbs} g, and fat {midFats} g to match exact total calorie/kcal. If not then try again." +
                $"\r\nNever change given format of 'Output Format:' and (no prose, no * or special symbols, no blank lines, never include any kind of note) and 'Final' should be at the end of result at any cost." +
                $"\r\nProvide rounded numeric values for macros, clear and concise final output without exceeding length constraints."

                );
            //$"You are a world-class diet coach. " +
            //$"Adjust the quantities of the following ingredients for {header} to meet these nutritional targets:" +
            //$"\r\n{calories} kcal, {midProteins} g protein, {midCarbs} g carbs, {midFats} g fat. Do not add new ingredients." +
            //$"\r\nIngredients:" +
            //$"\r\n{data}" +
            ////$"\r\n\r\nSum the calories from protein, carbs, and fat for each ingredient to ensure accuracy. " +
            //$"Adjust quantities to meet the nutritional targets. Use this format (no prose, no * or special symbols, no blank lines):" +
            //$"\r\n\r\n" +
            //$"Example Format:\r\nPaleo Egg Muffins:\r\nXXX kcal, XX g protein, XX g carbs, XX g fat\r\n[Name of ingredient 1] (volume measure)\r\n[Name of ingredient 1] (volume measure)\r\n..." +
            //$"\r\n\r\nShow the detailed calculation of kcal from the macros for each ingredient and the total. Once within range, output:" +
            //$"\r\n'Final:'" +
            //$"\r\n{header}\r\nXXX kcal, XX g protein, XX g carbs, XX g fat\r\n[Name of ingredient 1] (volume measure)\r\n[Name of ingredient 2] (volume measure)" +
            //$"\r\n..." +
            //$"\r\n\r\nImportant Note: Remember the most important thing that calories or kcal for ingredients and meal should not calculate automatically; " +
            //$"\r\nIt should be the sum of all macros (protein, carbs, fat) based on the given nutritional values:" +
            //$"\r\nInstructions:\r\n- Calories from protein = protein grams * 4\r\n- Calories from carbs = carbs grams * 4\r\n- Calories from fat = fat grams * 9" +
            ////$"\r\n\r\nIf totals are over 15% higher/lower than targets, then adjust again. Repeat until within range, especially calories should be within range. If max length is exceeded, then remove previous results of 'Removing...' or 'Adding...' and give output 'Within range...' with accurate calculation of kcal values and show it." +
            //$"\r\n\r\nNote: Ensure that 'Final:' output strictly follows the given example format and does not show macros with ingredients in final. Ensure no ingredient is repeated and do not exceed given ranges. The response must be clear, concise, and free of repetitions." +
            //$"Return the final result according to 'Important Note' with rounded values at any cost." +
            //$"\r\n\r\nAdditionally, show the calculation of kcal from protein, carbs, and fat as follows:" +
            //$"\r\n\r\nCalculation:\r\n- Ingredient : XX kcal from protein, XX kcal from carbs, XX kcal from fat" +
            //$"\r\n- [Name of ingredient 1] (volume measure) : XX kcal from protein, XX kcal from carbs, XX kcal from fat" +
            //$"\r\n- [Name of ingredient 2] (volume measure): XX kcal from protein, XX kcal from carbs, XX kcal from fat" +
            //$"\r\n..." +
            //$"\r\n\r\nTotal kcal: XX kcal (protein) + XX kcal (carbs) + XX kcal (fat) = XXX kcal (Rounded Values)" +
            //$"\r\n\r\nTotal protein: XX kcal (protein) / 4  = XXX protein g (Rounded Values)" +
            //$"\r\n\r\nTotal carbs: XX kcal (carbs) / 4 = XXX carbs g (Rounded Values)" +
            //$"\r\n\r\nTotal fat: XX kcal (fat) / 4 = XXX fat g (Rounded Values)" +
            //$"\r\n\r\nIf totals of kcal and macros are over 15% higher/lower than target, specially calories or kcal should be within range, then adjust again. Repeat process until within range. Never cross given target of calories/kcal. If max length is exceeded, then remove previous results of 'Removing...' or 'Adding...' and give output 'Within range...' at any cost." +

            //// $"\r\n\r\nIf totals are not within range then please repeat from start and make sure within targets." +
            //$"\r\n\r\nPlease make sure to use latest values for Total kcal,Total protein, Total carbs, and Total fat in 'final' result and final result should not show in ranges.");


            ////requestAI.AppendLine($"You are a world-class diet coach. I am your client. I want to only eat the following meal in {header} please don't add new ingredients at any cost. Just adjust quantity.");
            ////requestAI.AppendLine($"I have chosen a specific meal and require your expertise to ensure it meets my nutritional targets without repeating any ingredients in the summary. Here are the details:");
            ////requestAI.AppendLine($"\r\nI need only these ingredients without repeating any food twice:");
            //requestAI.AppendLine($"You are a world-class diet coach. Adjust the quantities of the following ingredients for {header} to meet these nutritional targets:");
            //requestAI.AppendLine($"\r\n{calories} kcal, {midProteins} g protein, {midCarbs} g carbs, {midFats} g fat. Do not add new ingredients." +
            //    $"\r\n\r\nIngredients:" +
            //    $"\r\n{data}" +
            //    //$"{data}");
            //    //requestAI.AppendLine($"\r\nBe ensure there are no duplicate foods in your meal");
            //    //requestAI.AppendLine($"\r\n\r\nNutritional Targets: {MinCalories}-{MaxCalories} kcal, {ProteinMinGrams}-{ProteinMaxGrams} g protein, {CarbsMinGrams}-{CarbsMaxGrams} g carbs, {FatMinGrams}-{FatMaxGrams} g fat." +
            //    //requestAI.AppendLine($"\r\n\r\nNutritional Targets: {midCalories} kcal, {midProteins} g protein, {midCarbs} g carbs, {midFats} g fat." +
            //    $"\r\nSum the calories from protein, carbs, and fat for each ingredient to ensure accuracy and all show it in result." +
            //    $"\r\n\r\nAdjust quantities to meet the nutritional targets. Use this format (no prose, no * or special symbols, no blank lines):" +
            //    $"\r\n\r\nExample Format:\r\nPaleo Egg Muffins:\r\n500 kcal, 45 g protein, 35 g carbs, 20 g fat\r\nEggs (3 large)\r\nSpinach (1 cup) \r\n...\r\nOnce within range, output:\r\n'Final:'\r\n{header}\r\nXXX kcal, XX g protein, XX g carbs, XX g fat\r\n[Name of ingredient 1](volume measure)\r\n[Name of ingredient 2](volume measure)\r\n..." +
            //    $"\r\nImportant Note: Remember the most important thing that calories or kcal for ingredients and meal should not calculate automatically it should be the sum of all macros(protein, carbs, fat) based on the given nutritional values:" +
            //    $"\r\nInstructions:" +
            //    $"\r\n   - Calories from protein = protein grams * 4\r\n   - Calories from carbs = carbs grams * 4\r\n   - Calories from fat = fat grams * 9" +

            //    $"\r\n\r\nIf totals are over 15% higher/lower than targets, then adjust again. Repeat until within range specially calories should be with in range. If max length is exceeded then remove previous results of 'Removing...' or 'Adding...' and give output 'Within range...' with accurate calculation of kcal values and show it." +
            //    //$"\r\n\r\nIf calories or macros are over 15% lower than the requirements, output 'Adding...' and adjust quantities." +
            //    //$"\r\n\r\nRepeat until macros match with given values but make sure if max length exceeded then remove previous details like 'Removing...' or 'Adding...' and output 'Within range...'" +
            //    //$"\r\n\r\nRepeat until macros are within the given ranges but make sure if max length exceeded then remove previous details like 'Removing...' or 'Adding...' and output 'Within range...'" +
            //    $"\r\n\r\nNote:Ensure that 'Final:' output strictly follow given example format and don't show macros with ingredients in final. Ensure no ingredient is repeated and do not exceed given ranges. The response must be clear, concise, and free of repetitions. Return the final result according to 'Important Note' at any cost."+
            //    $"\r\n\r\nDon't forget important note above and please make sure to calculate kcal should be the sum of macros values at all.") ;
            //    //$"\r\n\r\nThen, calculate calorie and macro totals. Show formulas.If calories or macros are over 10 % higher than the requirements, ouput 'Removing...' and remove ingredients to match the requirements. Calculate again. Repeat until within range and ouput: 'Within range...'" +
            //   // $"\r\n\r\nIf calories or macros are over 10 % lower than the requirements, ouput 'Adding...' and add quantities to match the requirements. Calculate again. Repeat until within range and ouput: 'Within range...'" +
            //   // $"\r\n\r\nNext, ouput 'Detailed draft:' and meal again as follows (no prose, no blank line):" +
            //   // $"\r\n\r\n[Name of ingredient 1] (volume measure) [XXX kcal, XX g protein, XX g carbs, XX g fat]" +
            //   // $"\r\n[Name of ingredient 2] (volume measure) [XXX kcal, XX g protein, XX g carbs, XX g fat]" +
            //   // $"\r\n..." +
            //   // $"\r\n\r\nSame meal name as above]" +
            //   // $"\r\nXXX kcal, XX g protein, XX g carbs, XX g fat" +
            //   // $"\r\n\r\nNext, ouput the 'Final:' and meal again as follows (no prose, no blank line):" +
            //   // $"\r\n\r\n{header}" +
            //   // //$"\r\n\r\n{capitalizedPlan}: [Same meal name as above]" +
            //   // $"\r\nXXX kcal, XX g protein, XX g carbs, XX g fat" +
            //   // $"\r\n[Name of ingredient 1] (volume measure)" +
            //   // $"\r\n[Name of ingredient 2] (volume measure)" +
            //   // $"\r\n..." +
            //   // $"\r\n[Stop here]" +
            //   // $"\r\n\r\nNote: Don't exceed given ranges and don't repeat any ingredient at any cost." +
            //   //// $"\r\n\r\nNote: Protein, carbs and fat strictly relies within given ranges. Don't exceed given ranges and don't repeat any ingredient at any cost." +
            //   // $"\r\nBe ensure the response is clear, concise, and free of any ingredient repetitions. Return Final result at any cost.");
            return requestAI.ToString();
        }
        catch (Exception ex)
        {
            return "";
        }
    }
    static (int, int, int) AdjustNutrients(int minCalories, int maxCalories, int proteinGrams, int carbsGrams, int fatGrams)
    {
        int proteinCalories = proteinGrams * 4;
        int carbsCalories = carbsGrams * 4;
        int fatCalories = fatGrams * 9;
        int totalCalories = proteinCalories + carbsCalories + fatCalories;

        int adjustedProtein = proteinGrams;
        int adjustedCarbs = carbsGrams;
        int adjustedFats = fatGrams;

        // Reduce fats first
        while (totalCalories > maxCalories && adjustedFats > 1)
        {
            adjustedFats--;
            totalCalories -= 9;
        }

        // Reduce carbs next
        while (totalCalories > maxCalories && adjustedCarbs > 1)
        {
            adjustedCarbs--;
            totalCalories -= 4;
        }

        // Reduce protein last
        while (totalCalories > maxCalories && adjustedProtein > 1)
        {
            adjustedProtein--;
            totalCalories -= 4;
        }

        int counter = 0;
        // Adjust upwards if totalCalories is below minCalories
        while (totalCalories < minCalories)
        {
            if (adjustedCarbs < carbsGrams)
            {
                adjustedCarbs++;
                totalCalories += 4;
            }
            else if (adjustedFats < fatGrams)
            {
                adjustedFats++;
                totalCalories += 9;
            }
            else if (adjustedProtein < proteinGrams)
            {
                adjustedProtein++;
                totalCalories += 4;
            }
            else
            {
                // If no more adjustments can be made to original values, adjust arbitrarily
                if (adjustedFats <= 1)
                {
                    adjustedFats++;
                    totalCalories += 9;
                }
                else if (adjustedCarbs <= 1)
                {
                    adjustedCarbs++;
                    totalCalories += 4;
                }
                else if (adjustedProtein <= 1)
                {
                    adjustedProtein++;
                    totalCalories += 4;
                }
                else
                {
                    counter++;
                }
            }

            if (counter > 4)
            {
                break;
            }
        }

        return (adjustedProtein, adjustedCarbs, adjustedFats);
    }
    private async Task<string> GetProteinMealtInfoRequestGPT4(string plan, decimal ProteinMinGrams, decimal CarbsMinGrams, decimal FatMinGrams, decimal MinCalories, string MealDetails)
    {
        string capitalizedPlan = char.ToUpper(plan[0]) + plan.Substring(1);
        StringBuilder requestAI = new StringBuilder();

        var data = MealDetails.Replace("[", "");
        data = data.Replace("]", "");
        data = data.Replace("Stop here", "");
        data = data.Replace("stop here", "");


        requestAI.AppendLine($"You are a world-class diet coach. I am your client. I want protein shake:");
        //requestAI.AppendLine($"\r\nBe careful and don't change milk or recipe. I need same as:");
        requestAI.AppendLine($"\r\n{data}");
        requestAI.AppendLine($"\r\nDon't change my recipe or milk. Also don't add any food You can only change quantity.");

        requestAI.AppendLine($"\r\nI want exactly {MinCalories} kcal, {ProteinMinGrams} g protein, {CarbsMinGrams} g carbs, {FatMinGrams} g fat." +
           $"\r\nNote: Please use same quantity of milk as well. Please at lease give me some result." +
           $"\r\nFormat your response exactly as follow (no prose, no blank line, nothing else):" +
            $"\r\n\r\n{capitalizedPlan}:" +
            $"\r\nXXX kcal, XX g protein, XX g carbs, XX g fat" +
            $"\r\n[Name of ingredient 1] (volume measure)" +
            $"\r\n[Name of ingredient 2] (volume measure)\r\n..." +
            $"\r\n[Stop here]");

        return requestAI.ToString();
    }

    private void ModalPage_OkButtonPress1(object sender, EventArgs e)
    {
        // Uncomment code please
        //DependencyService.Get<IOpenManager>().openMail();
    }

    private async void BtnHome_Clicked(object sender, EventArgs args)
    {
        App.BotList.Clear();
        ClearOptions();
        StartSetup();
    }

    private async void AskForNextMeal()
    {
        if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("email")?.Value))
            return;

            CustomPromptConfig customPromptConfig = new CustomPromptConfig($"In how many hours is your next meal?", "Enter how many", "Save",
         AppResources.Cancel,"",Keyboard.Numeric,"",4);

            customPromptConfig.ActionSelected += async (sender1, action) =>
            {
                if (action == PopupAction.OK)
                {
                    if (string.IsNullOrWhiteSpace(customPromptConfig.text) || Convert.ToDecimal(customPromptConfig.text, CultureInfo.InvariantCulture) < 1)
                    {
                        AskForNextMeal();
                        return;
                    }
                     decimal reps = Convert.ToDecimal(customPromptConfig.text.Replace(",", "."), CultureInfo.InvariantCulture);
                    if (reps > 24)
                    {
                        // AlertConfig alert = new AlertConfig()
                        // {
                        //     Message = "Enter valid hours, should be less then 24",
                        //     OkText = "Ok",
                        //     Title = "Error",
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        // };
                        // await UserDialogs.Instance.AlertAsync(alert);
                         await HelperClass.DisplayCustomPopupForResult("Error",
                            "Enter valid hours, should be less then 24","Ok","");
                        // AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        AskForNextMeal();
                        return;
                    }
                    _hour = reps;
                    SetNextMealPlan(reps);
                }else
                {
                    App.BotList.Clear();
                    //FabImage.IsVisible = true;
                }
            };
        await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
        // PromptConfig firsttimeExercisePopup = new PromptConfig()
        // {
        //     InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
        //     IsCancellable = true,
        //     Title = $"In how many hours is your next meal?",

        //     Placeholder = "Enter how many",
        //     OkText = "Save",
        //     MaxLength = 4,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = async (weightResponse) =>
        //     {
        //         if (weightResponse.Ok)
        //         {
        //             if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) <= 0)
        //             {
        //                 AskForNextMeal();
        //                 return;
        //             }

        //             decimal reps = Convert.ToDecimal(weightResponse.Value.Replace(",", "."), CultureInfo.InvariantCulture);
        //             if (reps > 24)
        //             {
        //                 // AlertConfig alert = new AlertConfig()
        //                 // {
        //                 //     Message = "Enter valid hours, should be less then 24",
        //                 //     OkText = "Ok",
        //                 //     Title = "Error",
        //                 //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //                 // };
        //                 // await UserDialogs.Instance.AlertAsync(alert);
        //                  await HelperClass.DisplayCustomPopupForResult("Error",
        //                     "Enter valid hours, should be less then 24","Ok","");
        //                 // AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //                 AskForNextMeal();
        //                 return;
        //             }
        //             _hour = reps;
        //             SetNextMealPlan(reps);
        //         }
        //         else
        //         {
        //             App.BotList.Clear();
        //             //FabImage.IsVisible = true;
        //         }
        //         // AskForNextMeal();

        //     }
        // };
        // firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
        // UserDialogs.Instance.Prompt(firsttimeExercisePopup);
    }

    protected void ExerciseRepsPopup_OnTextChanged(PromptTextChangedArgs obj)
    {
        const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
        bool IsValid = Regex.IsMatch(obj.Value, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
        {
            double result;
            obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
            double.TryParse(obj.Value, out result);
            obj.Value = result.ToString();
        }
    }

    private async void SetNextMealPlan(decimal hours)
    {
        try
        {

            await AddAnswer(hours.ToString());

            var str = "hour";
            if (hours > 1)
                str = "hours";
            //NotificationCenter.Current.Cancel(101);
            //var notification = new NotificationRequest
            //{
            //    NotificationId = 100,
            //    Title = "Eve Diet Coach",
            //    Description = "Time to log your meal!",
            //    ReturningData = "MealInfo", 
            //    Android = { IconSmallName = { ResourceName = "eve_notification" } },
            //    Schedule = { NotifyTime = DateTime.Now.AddSeconds((double)hours * 60 * 60) }// Used for Scheduling local notification, if not specified notification will show immediately.
            //};
            //NotificationCenter.Current.Show(notification);
            //var notification1 = new NotificationRequest
            //{
            //    NotificationId = 101,
            //    Title = "Eve Diet Coach",
            //    Description = "Did you forget to log your meal?",
            //    ReturningData = "MealInfo", // Returning data when tapped on notification.
            //    Android = { IconSmallName = { ResourceName = "eve_notification" } },
            //    Schedule = { NotifyTime = DateTime.Now.AddSeconds(((double)hours * 60 * 60) + (1 * 60 * 60)) } // Used for Scheduling local notification, if not specified notification will show immediately.
            //};
            //NotificationCenter.Current.Show(notification1);

            //var notification2 = new NotificationRequest
            //{
            //    NotificationId = 102,
            //    Title = "Eve Diet Coach",
            //    Description = "Did you forget to log your meal?",
            //    ReturningData = "MealInfo", 
            //    Android = { IconSmallName = { ResourceName = "eve_notification" } },
            //    Schedule = { NotifyTime = DateTime.Now.AddSeconds(((double)hours * 60 * 60) + (0.5 * 60 * 60)) } // Used for Scheduling local notification, if not specified notification will show immediately.
            //};
            //NotificationCenter.Current.Show(notification2);
            await AddQuestion($"OK, great—see you in {_hour} {str}!");
            //FabImage.IsVisible = true;

        }
        catch (Exception ex)
        {

        }

    }

    async Task AddQuestion(string question, bool isAnimated = true)
    {
        try
        {
            App.BotList.Add(new BotModel()
            {
                Question = question,
                Type = BotType.Ques
            });
            if (isAnimated)
            {
                await Task.Delay(300);
            }
            if (App.BotList.Any())
            {
                MainThread.BeginInvokeOnMainThread(() => {
                    var lastItem = App.BotList.LastOrDefault();
                    lstChats?.ScrollTo(lastItem, ScrollToPosition.MakeVisible, animate: false);
                    lstChats?.ScrollTo(lastItem, ScrollToPosition.End, animate: false);
                });
            }
        }
        catch (Exception ex)
        {

        }
    }
    async Task AddMealPlan(string question, bool isAnimated = true, bool isForGPT4 = false, bool scrollable = true, bool GPT4Change = false, bool SetImage = false, bool? isAdjusting = null)
    {
        if(!string.IsNullOrEmpty(question) && question.Contains("*"))
        {
            question = question.Replace("*", "");
        }
        App.BotList.Add(new BotModel()
        {
            Question = question,
            Type = BotType.MealPlanCard,
            Part2 = (GPT4Change) ? "true" : "false",
            Part3 = (isForGPT4) ? "true" : "false",
            SetsImage = (SetImage) ? "true" : "false",
            IsAdjusting = isAdjusting
        });
        if (isAnimated)
        {
            await Task.Delay(300);
        }
        if (scrollable)
        {
            MainThread.BeginInvokeOnMainThread(() => {
                lstChats.ScrollTo(App.BotList.LastOrDefault(), ScrollToPosition.MakeVisible, animate: false);
                lstChats.ScrollTo(App.BotList.LastOrDefault(), ScrollToPosition.End, animate: false);
            });
        }


    }
    void lstChats_ItemAppearing(System.Object sender, ItemVisibilityEventArgs e)
    {
    }

    async Task AddAnswer(string answer, bool isClearOptions = true)
    {
        try
        {
            App.BotList.Add(new BotModel()
            {
                Answer = answer,
                Type = BotType.Ans
            });

            lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.MakeVisible, animate: false);
            lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.End, animate: false);

            await Task.Delay(300);
        }
        catch (Exception ex)
        {

        }
    }

    async Task<CustomImageButton> AddCheckbox(string title, EventHandler handler, bool ischecked = false)
    {
        CustomImageButton imgBtn = new CustomImageButton()
        {
            Text = title,
            Source = ischecked ? "done.png" : "undone.png",
            BackgroundColor = Colors.White,
            TextFontColor = AppThemeConstants.OffBlackColor,
            Margin = new Thickness(25, 3),
            Padding = new Thickness(2)
        };
        imgBtn.Clicked += handler;
        stackOptions.Children.Add(imgBtn);
        return imgBtn;
    }

    async Task<Grid> AddOptions(string title, EventHandler handler, bool scrollable = true)
    {
        var grid = new Grid();
        var pancakeView = new Frame() { CornerRadius = 0, HeightRequest = 66, Margin = new Thickness(25, 1) };
        var gradientBrush = new LinearGradientBrush();
        gradientBrush.GradientStops.Add(new GradientStop(Color.FromHex("#0C2432"), (float)0.1));
        gradientBrush.GradientStops.Add(new GradientStop(Color.FromHex("#195276"), (float)1.0));
        pancakeView.Background = gradientBrush;
        grid.Children.Add(pancakeView);


        var btn = new DrMuscleButton()
        {
            Text = title,
            TextColor = Colors.White,
            BackgroundColor = Colors.Transparent,
            BorderWidth = 0,
            CornerRadius = 6,
            Margin = new Thickness(25, 5, 25, 5),
            FontAttributes = FontAttributes.Bold,
            BorderColor = Colors.Transparent,
            HeightRequest = 66
        };
        btn.Clicked += handler;

        grid.Children.Add(btn);
        stackOptions.Children.Add(grid);

        BottomViewHeight.Height = GridLength.Auto;
        if (App.BotList.Count > 0 && scrollable)
        {
            lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.MakeVisible, animate: false);
            lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.End, animate: false);
        }
        return grid;
    }

    void ToolbarItem_Clicked(System.Object sender, System.EventArgs e)
    {

    }
    void NewTapped(object sender, System.EventArgs e)
    {
        //ActionStack.IsVisible = !ActionStack.IsVisible;
    }

    private async Task ClearOptions()
    {
        //if (Device.RuntimePlatform.Equals(Device.iOS))
        //{
        //    stackOptions.Children.Clear();
        //    return;
        //}
        var count = stackOptions.Children.Count;
        for (var i = 0; i < count; i++)
        {
            stackOptions.Children.RemoveAt(0);
        }
        BottomViewHeight.Height = 5;
    }

    private async void UpdateSubscriptionData(SubscriptionModel subscription)
    {
        try
        {

            subscription.Email = LocalDBManager.Instance.GetDBSetting("email").Value;
            BooleanModel m = await DrMuscleRestClient.Instance.SubscriptionDetail(subscription);
            System.Diagnostics.Debug.WriteLine($"New Subscriptions added: {m.Result}");
        }
        catch (Exception)
        {

        }
    }

    private async void AddSubscriptionDataIfNotExist(SubscriptionModel subscription)
    {
        try
        {
            subscription.Email = LocalDBManager.Instance.GetDBSetting("email").Value;
            BooleanModel m = await DrMuscleRestClient.Instance.SubscriptionDetailIfNotExist(subscription);
            System.Diagnostics.Debug.WriteLine($"New Subscriptions added: {m.Result}");
        }
        catch (Exception ex)
        {

        }
    }
    //Setup Weight cards
    private async Task LoadSavedWeights()
    {
        try
        {

            //var chartSerie = new ChartSerie() { Name = "Weight chart", Color = SKColor.Parse("#38418C") };
            //List<ChartSerie> chartSeries = new List<ChartSerie>();
            //List<ChartEntry> entries = new List<ChartEntry>();
            var weightList = ((App)Application.Current).weightContext?.Weights;
            if (weightList != null)
            {
                SetupWeightTracker(weightList);
                if (weightList.Count < 2)
                {
                    //ImgWeight.IsVisible = true;
                    //LblWeightGoal2.Text = LblWeightGoal;
                    //LblWeightGoal2.FontSize = 15;
                    //LblWeightGoal2.TextColor = Color.FromHex("#AA000000");
                    //LblWeightGoal2.FontAttributes = FontAttributes.None;
                    //LblWeightGoal2.Margin = new Thickness(20, 11, 20, 20);
                    //LblTrackin2.IsVisible = true;
                    //chartViewWeight.IsVisible = false;
                    //WeightArrowText.IsVisible = false;
                    ////WeightBox.IsVisible = false;
                    //WeightBox2.IsVisible = false;
                }
                else
                {
                    //WeightArrowText.IsVisible = true;
                    //ImgWeight.IsVisible = false;
                    //chartViewWeight.IsVisible = true;
                    //LblWeightGoal2.Margin = new Thickness(20, 11, 20, 0);
                    //WeightArrowText.Margin = new Thickness(20, 11, 20, 20);
                    ////if (weightList.Count < 4)
                    //chartViewWeight.Margin = new Thickness(Device.Android == Device.RuntimePlatform ? -90 : -83, 0);

                    //if (weightList.Count < 3)
                    //{
                    //    weightList.Add(weightList.Last());
                    //}


                    //LblTrackin2.IsVisible = false;
                    ////StackWeightProgress.IsVisible = true;
                    ////Green
                    ////WeightArrowText.TextColor = Color.FromHex("#5CD196");
                    //WeightArrowText.Text = "0%";
                    //LblWeightGoal2.FontSize = 20;
                    //LblWeightGoal2.TextColor = Color.Black;
                    //LblWeightGoal2.FontAttributes = FontAttributes.Bold;
                    //LblWeightGoal2.Margin = new Thickness(20, 11, 20, 0);
                    //WeightArrowText.Margin = new Thickness(20, 11, 20, 20);
                    //WeightArrowText.Text = "Since last entry.";
                    //if (Math.Round(weightList[0].Weight, 2) == Math.Round(weightList[1].Weight, 2))
                    //{
                    //    LblWeightGoal2.Text = "Your weight is stable";
                    //}
                    //else if (Math.Round(weightList[0].Weight, 2) >= Math.Round(weightList[1].Weight, 2))
                    //{
                    //    var progress = (weightList[0].Weight - weightList[1].Weight) * 100 / weightList[0].Weight;

                    //    // WeightArrowText.Text = "Since last entry.";
                    //    LblWeightGoal2.Text = String.Format("Weight up {0}{1}%", "", Math.Round(progress)).ReplaceWithDot();


                    //}
                    //else
                    //{
                    //    //Red
                    //    //WeightArrowText.TextColor = Color.FromHex("#BA1C31");
                    //    var progress = (weightList[0].Weight - weightList[1].Weight) * 100 / weightList[0].Weight;
                    //    //WeightArrowText.Text = "Since last entry.";
                    //    LblWeightGoal2.Text = String.Format("Weight down {0}%", Math.Round(progress)).ReplaceWithDot().Replace("-", "");
                    //}
                    ////Set Weight data
                    //var days = (int)((DateTime)weightList[0].CreatedDate.Date - (DateTime)weightList[1].CreatedDate.Date).TotalDays;
                    //var dayStr = days > 1 ? "days" : "day";
                    //WeightArrowText.Text = $"In the last {days} {dayStr}.";

                    //var last3points = weightList.Take(3).Reverse();
                    //foreach (var weight in last3points)
                    //{
                    //    var isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";

                    //    decimal val = 0;
                    //    if (isKg)
                    //        val = Math.Round(weight.Weight, 2);
                    //    else
                    //        val = Convert.ToDecimal(Math.Round(new MultiUnityWeight((decimal)weight.Weight, "kg").Lb, 2));

                    //    entries.Add(new ChartEntry((float)val) { Label = weight.CreatedDate.ToString("MMM dd"), ValueLabel = val.ToString() });
                    //}
                    //chartSerie.Entries = entries;
                    //chartSeries.Add(chartSerie);

                    //chartViewWeight.Chart = new LineChart
                    //{
                    //    LabelOrientation = Orientation.Vertical,
                    //    ValueLabelOrientation = Orientation.Vertical,
                    //    LabelTextSize = 20,
                    //    ValueLabelTextSize = 20,
                    //    SerieLabelTextSize = 16,
                    //    LegendOption = SeriesLegendOption.None,
                    //    Series = chartSeries,
                    //};
                }
            }
        }
        catch { }
        //LoadSavedWeightFromServer();
    }

    private async Task LoadSavedWeightFromServer()
    {
        try
        {


            //var chartSerie = new ChartSerie() { Name = "Weight chart", Color = SKColor.Parse("#38418C") };
            //List<ChartSerie> chartSeries = new List<ChartSerie>();
            List<ChartEntry> entries = new List<ChartEntry>();

            var weightList = await DrMuscleRestClient.Instance.GetUserWeights();
            if (weightList != null)
            {
                ((App)Application.Current).weightContext.Weights = weightList;
                ((App)Application.Current).weightContext.SaveContexts();

                SetupWeightTracker(weightList);
            }
            //if (weightList.Count < 2)
            //{
            //    ImgWeight.IsVisible = true;
            //    LblWeightGoal2.Text = LblWeightGoal;
            //    LblWeightGoal2.FontSize = 15;
            //    LblWeightGoal2.TextColor = Color.FromHex("#AA000000");
            //    LblWeightGoal2.FontAttributes = FontAttributes.None;
            //    LblWeightGoal2.Margin = new Thickness(20, 11, 20, 20);

            //    chartViewWeight.IsVisible = false;
            //    WeightArrowText.IsVisible = false;

            //    //WeightBox.IsVisible = false;
            //    WeightBox2.IsVisible = false;
            //    return;
            //}
            //LblWeightGoal2.Margin = new Thickness(20, 11, 20, 0);
            //WeightArrowText.IsVisible = true;
            //ImgWeight.IsVisible = false;
            //chartViewWeight.IsVisible = true;

            ////if (weightList.Count < 4)
            //chartViewWeight.Margin = new Thickness(-83, 0);

            //if (weightList.Count < 3)
            //{
            //    weightList.Add(weightList.Last());
            //}
            //var days = (int)((DateTime)weightList[0].CreatedDate.Date - (DateTime)weightList[1].CreatedDate.Date).TotalDays;
            //var dayStr = days > 1 ? "days" : "day";
            //WeightArrowText.Text = $"In the last {days} {dayStr}.";

            //LblTrackin2.IsVisible = false;
            ////  StackWeightProgress.IsVisible = true;
            ////Green
            ////WeightArrowText.TextColor = Color.FromHex("#5CD196");
            //WeightArrowText.FontSize = 15;
            //WeightArrowText.TextColor = Color.FromHex("#AA000000");
            //WeightArrowText.FontAttributes = FontAttributes.None;
            //LblWeightGoal2.FontSize = 20;
            //LblWeightGoal2.TextColor = Color.Black;
            //LblWeightGoal2.FontAttributes = FontAttributes.Bold;
            //if (Math.Round(weightList[0].Weight, 2) == Math.Round(weightList[1].Weight, 2))
            //{
            //    LblWeightGoal2.Text = "Your weight is stable";
            //    //WeightArrowText.Text = "Since last entry.";
            //}
            //else if (Math.Round(weightList[0].Weight, 2) >= Math.Round(weightList[1].Weight, 2))
            //{
            //    var progress = (weightList[0].Weight - weightList[1].Weight) * 100 / weightList[0].Weight;

            //    //WeightArrowText.Text = "Since last entry.";
            //    LblWeightGoal2.Text = String.Format("Weight up {0}{1}%", "", Math.Round(progress)).ReplaceWithDot();

            //}
            //else
            //{
            //    //Red
            //    //WeightArrowText.TextColor = Color.FromHex("#BA1C31");
            //    var progress = (weightList[0].Weight - weightList[1].Weight) * 100 / weightList[0].Weight;
            //    // WeightArrowText.Text = "Since last entry.";
            //    LblWeightGoal2.Text = String.Format("Weight down {0}%", Math.Round(progress)).ReplaceWithDot().Replace("-", ""); ;
            //}
            //Set Weight data


            //var last3points = weightList.Take(3).Reverse();
            //foreach (var weight in last3points)
            //{
            //    var isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";

            //    decimal val = 0;
            //    if (isKg)
            //        val = Math.Round(weight.Weight, 2);
            //    else
            //        val = Convert.ToDecimal(Math.Round(new MultiUnityWeight((decimal)weight.Weight, "kg").Lb, 2));

            //    entries.Add(new ChartEntry((float)val) { Label = weight.CreatedDate.ToString("MMM dd"), ValueLabel = val.ToString() });
            //}
            //chartSerie.Entries = entries;
            //chartSeries.Add(chartSerie);

            //chartViewWeight.Chart = new LineChart
            //{
            //    LabelOrientation = Orientation.Vertical,
            //    ValueLabelOrientation = Orientation.Vertical,
            //    LabelTextSize = 20,
            //    ValueLabelTextSize = 20,
            //    SerieLabelTextSize = 16,
            //    LegendOption = SeriesLegendOption.None,
            //    Series = chartSeries,
            //};
        }
        catch { }
    }

    private async void SetupWeightTracker(List<UserWeight> userWeights)
    {
        try
        {

            if (userWeights == null)
                return;
            if (userWeights.Count == 0)
            {
                return;
            }
            bool isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? true : false;
            decimal _userBodyWeight = 0;
            if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
            {
                _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
                Config.CurrentWeight = _userBodyWeight.ToString();
            }

            decimal _targetIntake = 0;
            if (LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value != null)
            {
                _targetIntake = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("TargetIntake").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);


            }
            var startWeight = Convert.ToDecimal(userWeights.LastOrDefault().Weight, CultureInfo.InvariantCulture);

            var CurrentWeight = _userBodyWeight;

            decimal goalWeight = 0;

            //if (LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value != null)
            //{
            //    goalWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.Replace(",", "."), CultureInfo.InvariantCulture);
            //    btnUpdateGoal2.IsVisible = false;
            //    btnUpdateMealPlan.IsVisible = true;
            //}
            //else
            //{
            //    btnUpdateGoal2.IsVisible = true;
            //    btnUpdateMealPlan.IsVisible = false;
            //    LblWeightTip2.Text = "Goal weight not set";
            //    LblWeightTipText2.Text = "";// "Update your goal weight to see the weight tips here.";
            //}
            if (_targetIntake == 0)
            {
                var userInfo = await DrMuscleRestClient.Instance.GetTargetIntakeWithoutLoader();
                if (userInfo?.TargetIntake != null)
                {
                    LocalDBManager.Instance.SetDBSetting("TargetIntake", userInfo.TargetIntake.ToString());
                    _targetIntake = (decimal)userInfo.TargetIntake;
                }
            }
            var togoOfGoal = "";
            //if (goalWeight == 0)
            //{
            //    if (_targetIntake != 0)
            //        LblTargetIntake2.Text = $"{Math.Round(_targetIntake)} cal/day";
            //    else
            //        LblTargetIntake2.Text = $"Calories not set";
            //    btnUpdateGoal.IsVisible = true;
            //    btnMealPlan.IsVisible = false;
            //}
            //else
            //{
            //    if (CurrentWeight < goalWeight)
            //        LblTargetIntake2.Text = $"{Math.Round(_targetIntake)} cal/day to build muscle";
            //    else
            //        LblTargetIntake2.Text = $"{Math.Round(_targetIntake)} cal/day to lose fat";
            //    btnUpdateGoal.IsVisible = false;
            //    btnMealPlan.IsVisible = true;
            //}

            if (_targetIntake == 0)
            {
                //LblCarbText2.Text = "";
                //LblProteinText2.Text = "";
                //LblFatText2.Text = "";
            }
            else
            {
                string macros = $"Target intake: {_targetIntake}\n";
                macros += $"\nProtein range: ";
                var LblProteinText2 = $"{Math.Round(_userBodyWeight * (decimal)1.8)} - {Math.Round(_userBodyWeight * (decimal)2.5)} g";
                macros += LblProteinText2;
                var newLowerTargetIntake = _targetIntake - (Math.Round(_userBodyWeight * (decimal)2.15) * 4);
                var newHigherTargetIntake = _targetIntake - (Math.Round(_userBodyWeight * (decimal)2.15) * 4);

                var LblCarbText2 = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 4)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 4)} g";
                macros += $"\nCarbs range: {LblCarbText2}";

                var LblFatText2 = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 9)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 9)} g";
                macros += $"\nFats range: {LblFatText2}";


                //calculate macros from Eve
                //string macros = $"Target intake: {_targetIntake}\n";
                //macros += $"\nProtein range: ";
                //var protein = $"{Math.Round(_userBodyWeight * (decimal)1.8)} - {Math.Round(_userBodyWeight * (decimal)2.5)} g";
                //macros += protein;
                //var newLowerTargetIntake = _targetIntake - (Math.Round(_userBodyWeight * (decimal)2.15) * 4);
                //var newHigherTargetIntake = _targetIntake - (Math.Round(_userBodyWeight * (decimal)2.15) * 4);

                //var carbs = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 4)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 4)} g";
                //macros += $"\nCarbs range: {carbs}";

                //var fats = $"{Math.Round((double)newLowerTargetIntake * 0.2 / 9)} - {Math.Round((double)newHigherTargetIntake * 0.8 / 9)} g";
                //macros += $"\nFats range: {fats}";


                //macros += $"Carbs range= ";
                //var LblCarbText2 = Math.Round((double)_targetIntake * 0.45 / 3.87) + "-" + Math.Round((double)_targetIntake * 0.65 / 3.87) + " g";
                ////LblCarbText2.Text = Math.Round((double)_targetIntake * 0.45 / 3.87) + "-" + Math.Round((double)_targetIntake * 0.65 / 3.87) + " g";
                //CarbRecom = LblCarbText2;

                //macros += $"\nProtein range= ";
                //var LblProteinText2 = "";
                ////LblProteinText2.Text = Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb * (decimal)1.3) + "-" + Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb * (decimal)1.5) + " g";
                //if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
                //{
                //    LblProteinText2 = Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Kg * (decimal)1.6) + "-" + Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Kg * (decimal)2.2) + " g";
                //}
                //else
                //{
                //    LblProteinText2 = Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb * (decimal)0.7) + "-" + Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb * (decimal)1.0) + " g";
                //}

                //macros += $"\nFats range= ";
                //var LblFatText2 = Math.Round((double)_targetIntake * 0.2 / 9) + "-" + Math.Round((double)_targetIntake * 0.35 / 9) + " g";
                //LblFatText2.Text = Math.Round((double)_targetIntake * 0.2 / 9) + "-" + Math.Round((double)_targetIntake * 0.35 / 9) + " g";
                ProteinRecom = LblProteinText2;
                CarbRecom = LblCarbText2;
                FatRecom = LblFatText2;
            }

            //if (isKg)
            //{

            //    LblCurrentText2.Text = string.Format("{0:0.##} {1}", Math.Round(CurrentWeight, 2), "kg");

            //    LblGoalText2.Text = goalWeight == 0 ? "?" : string.Format("{0:0.##} {1}", Math.Round(goalWeight, 2), "kg");
            //    togoOfGoal = goalWeight == 0 ? "?" : string.Format("{0:0.##}", Math.Round(goalWeight, 2));
            //    LblStartText2.Text = string.Format("{0:0.##} {1}", Math.Round(startWeight, 2), "kg");


            //}
            //else
            //{
            //    LblCurrentText2.Text = string.Format("{0:0.##} {1}", Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb, 2), "lbs");

            //    LblGoalText2.Text = goalWeight == 0 ? "?" : string.Format("{0:0.##} {1}", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2), "lbs");

            //    togoOfGoal = goalWeight == 0 ? "?" : string.Format("{0:0.##}", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2));
            //    LblStartText2.Text = string.Format("{0:0.##} {1}", Math.Round(new MultiUnityWeight((decimal)startWeight, "kg").Lb, 2), "lbs");
            //}

            //bool isGain = false;
            //if (CurrentWeight < goalWeight)
            //{
            //    isGain = true;

            //}
            if (goalWeight != 0)
            {
                //string Gender = LocalDBManager.Instance.GetDBSetting("gender").Value.Trim();
                //var creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                //decimal weeks = 0;
                //if (creationDate != null)
                //{
                //    weeks = (int)(DateTime.Now - creationDate).TotalDays / 7;
                //}
                //int lowReps = 0;
                //int highreps = 0;
                //try
                //{
                //    lowReps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsminimum").Value);
                //    highreps = int.Parse(LocalDBManager.Instance.GetDBSetting("repsmaximum").Value);
                //}
                //catch (Exception)
                //{

                //}
                //var result = "";
                //if (lowReps >= 5 && highreps <= 12)
                //    result = "This helps you build muscle and strength.";
                //else if (lowReps >= 8 && highreps <= 15)
                //    result = "This helps you build muscle and burn fat.";
                //else if (lowReps >= 5 && highreps <= 15)
                //    result = "This helps you build muscle.";
                //else if (lowReps >= 12 && highreps <= 20)
                //    result = "This helps you burn fat.";
                //else if (highreps >= 16)
                //    result = "This helps you build muscle and burn fat.";
                //else
                //{
                //    if (LocalDBManager.Instance.GetDBSetting("Demoreprange") != null)
                //    {
                //        if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscle")
                //        {
                //            result = "This helps you build muscle.";
                //        }
                //        else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "BuildMuscleBurnFat")
                //        {
                //            result = "This helps you build muscle and burn fat.";
                //        }
                //        else if (LocalDBManager.Instance.GetDBSetting("Demoreprange").Value == "FatBurning")
                //        {
                //            result = "This helps you burn fat.";
                //        }
                //    }
                //}
                //decimal rate = (decimal)2.3;
                //if (result.Contains("build muscle and burn fat"))
                //{
                //    rate = (decimal)2.4;
                //}
                //else if (result.Contains("build muscle"))
                //{
                //    rate = (decimal)2.3;
                //}
                //else if (result.Contains("burn fat"))
                //{
                //    rate = (decimal)2.4;
                //}
                //decimal gainDouble = 0;
                //if (Gender == "Man")
                //{
                //    if (weeks <= 18)
                //        gainDouble = ((decimal)0.015 - (decimal)0.000096899 * weeks) * CurrentWeight;
                //    else if (weeks > 18 && weeks <= 42)
                //        gainDouble = ((decimal)0.011101 - (decimal)0.000053368 * weeks) * CurrentWeight;
                //    else if (weeks > 42)
                //        gainDouble = (decimal)0.00188 * CurrentWeight;
                //}
                //else
                //{
                //    if (weeks <= 18)
                //        gainDouble = (((decimal)0.015 - (decimal)0.000096899 * weeks) * CurrentWeight) / 2;
                //    else if (weeks > 18 && weeks <= 42)
                //        gainDouble = (((decimal)0.011101 - (decimal)0.000053368 * weeks) * CurrentWeight) / 2;
                //    else if (weeks > 42)
                //        gainDouble = ((decimal)0.00188 * CurrentWeight) / 2;
                //}
                ////Convert to day
                //gainDouble = gainDouble / 30;


                //decimal loseDouble = ((decimal)0.01429 * CurrentWeight) / 30;


                //string gain = string.Format("{0:0.##}", isKg ? Math.Round(gainDouble, 2) : Math.Round(new MultiUnityWeight(gainDouble, WeightUnities.kg).Lb, 2));

                //string lose = string.Format("{0:0.##}", isKg ? Math.Round(loseDouble, 2) : Math.Round(new MultiUnityWeight(loseDouble, WeightUnities.kg).Lb, 2));
                //var weekText = weeks <= 1 ? "week" : "weeks";
                //int days = 0;

                //if (userWeights.Count > 1)
                //{
                //    days = Math.Abs((int)(userWeights[1].CreatedDate.Date - userWeights.First().CreatedDate.Date).TotalDays);
                //    startWeight = Convert.ToDecimal(userWeights[1].Weight, CultureInfo.InvariantCulture);
                //}

                //double totalChanged = 0;
                //if (userWeights.Count > 1)
                //    totalChanged = (double)(((userWeights.First().Weight - userWeights[1].Weight) * 100) / userWeights[1].Weight);
                //double dailyChanged = (double)totalChanged;

                //if (days != 0)
                //    dailyChanged = totalChanged / days;
                //bool isLess = false;
                //if (days == 0)
                //    days = 1;
                //if (CurrentWeight > goalWeight)
                //{
                //    //Lose weight
                //    if (Math.Round(CurrentWeight, 1) >= Math.Round(startWeight, 1))
                //    {
                //        isLess = true;
                //    }
                //    else
                //    {
                //        if (loseDouble > (Math.Abs((startWeight - CurrentWeight) / days)))
                //            isLess = true;
                //        else
                //            isLess = false;

                //    }
                //}
                //else
                //{
                //    //Gain
                //    if (Math.Round(CurrentWeight, 1) <= Math.Round(startWeight, 1))
                //    {
                //        isLess = false;
                //    }
                //    else
                //    {
                //        if (gainDouble < (Math.Abs((startWeight - CurrentWeight) / days)))
                //            isLess = true;
                //        else
                //            isLess = false;
                //    }

                //}

                //var lessMoreText = "";

                //if (CurrentWeight <= goalWeight)
                //{
                //    //Gain weight
                //    if (isLess)
                //    {
                //        lessMoreText = "so you're probably gaining fat.";//$"You're probably gaining fat. Eat less (but aim for {Math.Round(CurrentWeight * rate)} g protein / day).";
                //    }
                //    else
                //    {

                //        lessMoreText = $"so you could speed that up by eating more."; //$"You're probably leaving muscle on the table. Eat more (and aim for {Math.Round(CurrentWeight * rate)} g protein / day).";
                //    }
                //}
                //else
                //{
                //    //lose weight
                //    if (isLess)
                //    {
                //        //lessMoreText = $"you could speed that up by eating less. And aim to eat {Math.Round(CurrentWeight * rate)} g of protein a day.";
                //        lessMoreText = "so you could speed that up by eating less.";//$"To speed that up, eat less (but aim for {Math.Round(CurrentWeight * rate)} g protein / day).";

                //    }
                //    else
                //    {
                //        //lessMoreText = $"you're probably losing muscle mass too. Eat more to prevent that. And aim to eat {Math.Round(CurrentWeight * rate)} g of protein a day.";
                //        lessMoreText = "so you're probably losing muscle mass.";//$"You're probably losing muscle mass. Eat more (and aim for {Math.Round(CurrentWeight * rate)} g protein / day).";
                //    }
                //}

                //var goalGainWeight = string.Format("{0:0.##}", Math.Round(CurrentWeight * rate) / 1000, 2);


                //var gainWeight = string.Format("{0:0.##}", Math.Abs(Math.Round(CurrentWeight - startWeight, 2)));
                //var gainInaMonth = Math.Round(CurrentWeight - startWeight, 2) / days;
                //var gainInaMonthText = string.Format("{0:0.##}", Math.Round(Math.Abs((CurrentWeight - startWeight)) / days, 2));
                //var gainDiffernece = string.Format("{0:0.##}", Math.Abs(Math.Round(goalWeight - startWeight, 2)));
                //var remainDiffernece = string.Format("{0:0.##}", Math.Abs(Math.Round(goalWeight - CurrentWeight, 2)));
                //var massunit = "kg";
                //if (!isKg)
                //{
                //    massunit = "lbs";
                //    gainWeight = string.Format("{0:0.##}", Math.Abs(Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb, 2)));

                //    gainDiffernece = string.Format("{0:0.##}", Math.Abs(Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb, 2)));

                //    remainDiffernece = string.Format("{0:0.##}", Math.Abs(Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb - new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb, 2)));
                //    goalGainWeight = string.Format("{0:0.##}", Math.Round(new MultiUnityWeight(CurrentWeight * rate, "kg").Lb / (decimal)453.59237, 2));

                //    gainInaMonthText = string.Format("{0:0.##}", Math.Abs(Math.Round((new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb) / days, 2)));
                //}

                //if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1) && Math.Round(CurrentWeight, 1) == Math.Round(goalWeight, 1))
                //{
                //    LblWeightTip2.Text = "Your weight is stable";
                //    LblWeightTipText2.Text = $"At {LblCurrentText2.Text}, your weight is stable. Aim for {Math.Round(CurrentWeight * rate)} g protein / day.";
                //    LblWeightToGo2.Text = string.Format("Success! {0} 💪", LblGoalText2.Text);
                //}
                //else if (Math.Round(CurrentWeight, 1) == Math.Round(goalWeight, 1))
                //{

                //    LblWeightTip2.Text = string.Format("{0} {1} {2} in {3} {4}", gainWeight, massunit, CurrentWeight > startWeight ? "gained" : "lost", days, days > 1 ? "days" : "day");
                //    LblWeightToGo2.Text = string.Format("Success! {0} 💪", LblGoalText2.Text);

                //    LblWeightTipText2.Text = $"At {LblCurrentText2.Text}, you're at your goal weight. Aim for {Math.Round(CurrentWeight * rate)} g protein / day.";

                //}
                //else if (CurrentWeight < goalWeight)
                //{
                //    //Gain weight



                //    if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1))
                //    {
                //        LblWeightTip2.Text = "Your weight is stable";
                //        // LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. Since you have been with us for {weeks} {weekText}, you should gain about {gain} {massunit} a month. Currently, you're at your starting weight. So, {lessMoreText}";
                //        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";
                //        LblWeightTipText2.Text = $"Since you have been with us for {weeks} {weekText}, you should gain about {gain} {massunit} a day. your weight is stable. {lessMoreText}";
                //    }
                //    else if (CurrentWeight > startWeight)
                //    {

                //        LblWeightTip2.Text = string.Format("{0} {1} gained in {2} {3}", gainWeight, massunit, days, days > 1 ? "days" : "day");
                //        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";

                //        LblWeightTipText2.Text = $"You're gaining {gainInaMonthText} {massunit} a day. Since you have been with us for {weeks} {weekText}, you should gain about {gain} {massunit} a day. {lessMoreText}";
                //        LblWeightTipText2.Text = LblWeightTipText2.Text.Replace("so ", "So, ");
                //        //   LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. Since you have been with us for {weeks} {weekText}, you should gain about {gain} {massunit} a month. Currently, you're gaining {gainInaMonthText} {massunit} a month. So, {lessMoreText}";
                //    }
                //    else if (CurrentWeight < startWeight)
                //    {
                //        LblWeightTip2.Text = string.Format("{0} {1} lost in {2} {3}", gainWeight, massunit, days, days > 1 ? "days" : "day");

                //        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";

                //        // LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. Since you have been with us for {weeks} {weekText}, you should gain about {gain} {massunit} a month. Currently, you're losing {gainInaMonthText} {massunit} a month. So, {lessMoreText}";
                //        LblWeightTipText2.Text = $"You're losing {gainInaMonthText} {massunit} a day. Since you have been with us for {weeks} {weekText}, you should gain about {gain} {massunit} a day. {lessMoreText}";
                //        LblWeightTipText2.Text = LblWeightTipText2.Text.Replace("so ", "So, ");
                //    }
                //}
                //else
                //{
                //    //Loose weight
                //    if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1))
                //    {
                //        LblWeightTip2.Text = "Your weight is stable";
                //        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";

                //        //    LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. At {LblCurrentText1.Text}, you should gain about {lose} {massunit} a month. Currently, you're at your starting weight. So, {lessMoreText}";

                //        //  LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. At {LblCurrentText1.Text}, you should gain about {lose} {massunit} a month. Currently, you're at your starting weight. So, {lessMoreText}";
                //        LblWeightTipText2.Text = $"At {LblCurrentText2.Text}, you should lose about {lose} {massunit} a day. your weight is stable. {lessMoreText}";
                //        LblWeightTipText2.Text = LblWeightTipText2.Text.Replace("so ", "So, ");
                //        //   LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. To know if you're eating enough calories, track your waist circumference every week. If it's going up, {lessMoreText}";

                //    }
                //    else if (CurrentWeight > startWeight)
                //    {

                //        LblWeightTip2.Text = string.Format("{0} {1} gained in {2} {3}", gainWeight, massunit, days, days > 1 ? "days" : "day");

                //        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";

                //        //LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. At {LblCurrentText1.Text}, you should lose about {lose} {massunit} a month. Currently, you're gaining {gainInaMonthText} {massunit} a month. So, {lessMoreText}";

                //        LblWeightTipText2.Text = $"You're gaining {gainInaMonthText} {massunit} a day. At {LblCurrentText2.Text}, you should lose about {lose} {massunit} a day. {lessMoreText}";
                //        LblWeightTipText2.Text = LblWeightTipText2.Text.Replace("so ", "So, ");
                //        //LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. To know if you're eating enough calories, track your waist circumference every week. If it's going up, {lessMoreText}";
                //    }
                //    else if (CurrentWeight < startWeight)
                //    {
                //        LblWeightTip2.Text = string.Format("{0} {1} lost in {2} {3}", gainWeight, massunit, days, days > 1 ? "days" : "day");

                //        LblWeightToGo2.Text = $"{remainDiffernece} {massunit} to goal of {togoOfGoal}";

                //        LblWeightTipText2.Text = $"You're losing {gainInaMonthText} {massunit} a day. At {LblCurrentText2.Text}, you should lose about {lose} {massunit} a day. {lessMoreText}";
                //        LblWeightTipText2.Text = LblWeightTipText2.Text.Replace("so ", "So, ");
                //        //LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. At {LblCurrentText1.Text}, you should lose about {lose} {massunit} a month. Currently, you're losing {gainInaMonthText} {massunit} a month. So, {lessMoreText}";
                //        //LblWeightTipText2.Text = $"{remainDiffernece} {massunit} to go. To know if you're eating enough calories, track your waist circumference every week. If it's going up, {lessMoreText}";
                //    }

                //}
            }
            else
            {
                //int days = 0;
                //if (userWeights.Count > 1)
                //{
                //    days = Math.Abs((int)(userWeights[1].CreatedDate.Date - userWeights.First().CreatedDate.Date).TotalDays);
                //    startWeight = Convert.ToDecimal(userWeights[1].Weight, CultureInfo.InvariantCulture);
                //}


                //if (days == 0)
                //    days = 1;
                //var gainWeight = string.Format("{0:0.##}", Math.Abs(Math.Round(CurrentWeight - startWeight, 2)));
                //var gainInaMonth = Math.Round(CurrentWeight - startWeight, 2) / days * 30;

                //var massunit = "kg";
                //if (!isKg)
                //{
                //    massunit = "lbs";
                //    gainWeight = string.Format("{0:0.##}", Math.Abs(Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb, 2)));

                //}
                //if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1))
                //{
                //    LblWeightToGo2.Text = "Your are at your starting weight";

                //    LblWeightTipText2.Text = $"";
                //}
                //else if (CurrentWeight > startWeight)
                //{
                //    LblWeightToGo2.Text = string.Format("{0} {1} gained in {2} {3}", gainWeight, massunit, days, days > 1 ? "days" : "day");
                //}
                //else if (CurrentWeight < startWeight)
                //{
                //    LblWeightToGo2.Text = string.Format("{0} {1} lost in {2} {3}", gainWeight, massunit, days, days > 1 ? "days" : "day");
                //}
            }
            //LblWeightToGo1.Text = LblWeightToGo2.Text;
            //LblWeightTip1.Text = LblWeightTip2.Text;
            //LblWeightTipText1.Text = LblWeightTipText2.Text;
            //if (Math.Round(CurrentWeight, 1) == Math.Round(startWeight, 1))
            //{
            //    LbltrackerText2.Text = $"Your are at your starting weight";

            //    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Green;

            //}

            //else if (Math.Round(CurrentWeight, 1) == Math.Round(goalWeight, 1))
            //{
            //    if (isKg)
            //        LbltrackerText2.Text =
            //            string.Format("Success! {0:0.##} kg 💪", Math.Round(CurrentWeight, 2));
            //    else
            //        LbltrackerText2.Text = string.Format("Success! {0:0.##} lbs 💪", Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb, 2));

            //    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Green;

            //}
            //else if (CurrentWeight > goalWeight && goalWeight > startWeight)
            //{
            //    //Progress smoothly

            //    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Green;

            //    if (isKg)
            //    {
            //        LbltrackerText2.Text = string.Format("Success! {0:0.##} kg above goal", Math.Round(CurrentWeight - goalWeight, 2));


            //    }
            //    else
            //    {
            //        LbltrackerText2.Text = string.Format("Success! {0:0.##} lbs above goal", Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)goalWeight, "kg").Lb, 2));

            //    }
            //}
            //else if (CurrentWeight < goalWeight && goalWeight < startWeight)
            //{
            //    //Progress smoothly

            //    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Green;

            //    if (isKg)
            //    {
            //        LbltrackerText2.Text = string.Format("Success! {0:0.##} kg under goal", Math.Round(goalWeight - CurrentWeight, 2));

            //        //LbltrackerText2.Text = LbltrackerText1.Text;
            //    }
            //    else
            //    {
            //        LbltrackerText2.Text = string.Format("Success! {0:0.##} lbs under goal", Math.Round(new MultiUnityWeight((decimal)goalWeight, "kg").Lb - new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb, 2));
            //        //LbltrackerText2.Text = LbltrackerText1.Text;
            //    }
            //}
            //else if (CurrentWeight > startWeight)
            //{
            //    //Overweight
            //    if (goalWeight < CurrentWeight)
            //    {
            //        LbltrackerText2.TextColor =  Color.Red;
            //        LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Red;
            //    }
            //    else
            //    {
            //        LbltrackerText2.TextColor =  Color.Green;
            //        LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Green;
            //    }
            //    if (isKg)
            //    {
            //        LbltrackerText2.Text = string.Format("You have gained {0:0.##} kg", Math.Round(CurrentWeight - startWeight, 2));
            //    }
            //    else
            //    {
            //        LbltrackerText2.Text = string.Format("You have gained {0:0.##} lbs", Math.Round(new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb - new MultiUnityWeight((decimal)startWeight, "kg").Lb, 2));
            //    }
            //}

            //else if (CurrentWeight < startWeight)
            //{
            //    //Low weight
            //    if (goalWeight < CurrentWeight)
            //    {

            //        LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Green;
            //    }
            //    else
            //    {

            //        LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Red;
            //    }
            //    if (isKg)
            //    {
            //        LbltrackerText2.Text = string.Format("You have lost {0:0.##} kg", Math.Round(startWeight - CurrentWeight, 2));

            //    }
            //    else
            //    {
            //        LbltrackerText2.Text =
            // string.Format("You have lost {0:0.##} lbs", Math.Round(new MultiUnityWeight((decimal)startWeight, "kg").Lb - new MultiUnityWeight((decimal)CurrentWeight, "kg").Lb, 2));

            //    }
            //    //if (!isGain)
            //    //{
            //    //    LbltrackerText1.TextColor = FrmTracker1.BackgroundColor = Color.Green;
            //    //    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Green;

            //    //}
            //}


            //else if (CurrentWeight == startWeight)
            //{
            //    LbltrackerText2.Text = $"Your weight is stable";
            //    LbltrackerText2.TextColor = FrmTracker2.BackgroundColor = Color.Green;
            //    LbltrackerText2.Text = LbltrackerText2.Text;
            //}

        }
        catch (Exception ex)
        {

        }
    }

    async void BtnLearnMore_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            if (await CheckTrialUserAsync())
                return;
            LearnPage page = new LearnPage();
            await Navigation.PushAsync(page);
            //await PagesFactory.PushAsync<LearnPage>();
        }
        catch(Exception ex)
        {

        }
    }


/* Unmerged change from project 'DrMaxMuscle (net8.0-ios)'
Before:
    private bool CheckTrialUser()
After:
    private bool CheckTrialUserAsync()
*/
    private async Task<bool> CheckTrialUserAsync()
    {
        try
        {
            if (App.IsFreePlan)
            {
                var ShowPopUp = await HelperClass.DisplayCustomPopup("You discovered a premium feature!", "Upgrading will unlock custom coaching tips based on your goals and progression.",
             "Upgrade", "Maybe later");
                var res = false;
                ShowPopUp.ActionSelected += async (sender, action) =>
                {

                    try
                    {
                        if (action == Views.PopupAction.OK)
                        {
                            SubscriptionPage page2 = new SubscriptionPage();
                            page2.OnBeforeShow();
                            Navigation.PushAsync(page2);
                            //PagesFactory.PushAsync<SubscriptionPage>();
                        }
                    }
                    catch (Exception ex)
                    {

                    }

                };

                // ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                // {
                //     Message = "Upgrading will unlock custom coaching tips based on your goals and progression.",
                //     Title = "You discovered a premium feature!",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Upgrade",
                //     CancelText = "Maybe later",
                //     OnAction = async (bool ok) =>
                //     {
                //         if (ok)
                //         {
                //             SubscriptionPage page2= new SubscriptionPage();
                //             page2.OnBeforeShow();
                //             Navigation.PushAsync(page2);
                //             //PagesFactory.PushAsync<SubscriptionPage>();
                //         }
                //         else
                //         {

                //         }
                //     }
                // };
                // UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
        }
        catch (Exception ex)
        {

        }
        return App.IsFreePlan;
    }

    bool isMealPlan = false;

    async void GetMealPlan_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");
                return;
            }
            if (isMealPlan)
                return;
            //if (App.IsMealPlan)
            //{
            isMealPlan = true;
            var currentPage = Navigation.NavigationStack.LastOrDefault();
            if (currentPage is MealInfoPage)
            {
                Navigation.RemovePage(currentPage);
            }

            MealInfoPage mealInfoPage = new MealInfoPage();
            mealInfoPage.OnBeforeShow();
            await Navigation.PushAsync(mealInfoPage);
            //await PagesFactory.PushAsync<MealInfoPage>();
            isMealPlan = false;
            //}
            //else
            //{
            //    await PagesFactory.PushAsync<SubscriptionPage>();
            //}
        }
        catch (Exception ex)
        {

        }
    }

    async void EnterWeight_Clicked(System.Object sender, System.EventArgs e)
    {
        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });
            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");
            return;
        }

        CustomPromptConfig customPromptConfig = new CustomPromptConfig("Update body weight", "Tap to enter your weight", AppResources.Ok,
         AppResources.Cancel,"",Keyboard.Numeric,"",7);

            customPromptConfig.ActionSelected += async (sender1, action) =>
            {
                if (action == PopupAction.OK)
                {
                    if (string.IsNullOrWhiteSpace(customPromptConfig.text) || Convert.ToDecimal(customPromptConfig.text, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                     var weightText = customPromptConfig.text.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);

                    LocalDBManager.Instance.SetDBSetting("BodyWeight", new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg.ToString().Replace(",", "."));
                    var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                    var weights = new MultiUnityWeight(value, "kg");
                    // LblBodyweight.Text = string.Format("{0:0.00}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? weights.Kg : weights.Lb);
                    await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
                    {
                        BodyWeight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value)
                    });
                    // WeightBox.IsVisible = false;
                    if (Device.RuntimePlatform.Equals(Device.iOS))
                    {
                        IHealthData _healthService = DependencyService.Get<IHealthData>();
                        await _healthService.GetWeightPermissionAsync(async (r) =>
                        {
                            var a = r;
                            if (r)
                            {
                                _healthService.SetWeight(LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? (double)Math.Round(weights.Kg, 2) : (double)Math.Round(weights.Lb, 2));
                            }
                        });
                    }
                    LoadSavedWeightFromServer();
                    return;
                }
            };
        await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);

        // PromptConfig firsttimeExercisePopup = new PromptConfig()
        // {
        //     InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
        //     IsCancellable = true,
        //     Title = "Update body weight",
        //     MaxLength = 7,
        //     Placeholder = "Tap to enter your weight",
        //     OkText = AppResources.Ok,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = async (weightResponse) =>
        //     {
        //         if (weightResponse.Ok)
        //         {
        //             if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
        //             {
        //                 return;
        //             }
        //             var weightText = weightResponse.Value.Replace(",", ".");
        //             decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);

        //             LocalDBManager.Instance.SetDBSetting("BodyWeight", new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg.ToString().Replace(",", "."));
        //             var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
        //             var weights = new MultiUnityWeight(value, "kg");
        //             // LblBodyweight.Text = string.Format("{0:0.00}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? weights.Kg : weights.Lb);
        //             await DrMuscleRestClient.Instance.SetUserBodyWeight(new UserInfosModel()
        //             {
        //                 BodyWeight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value)
        //             });
        //             // WeightBox.IsVisible = false;
        //             if (Device.RuntimePlatform.Equals(Device.iOS))
        //             {
        //                 IHealthData _healthService = DependencyService.Get<IHealthData>();
        //                 await _healthService.GetWeightPermissionAsync(async (r) =>
        //                 {
        //                     var a = r;
        //                     if (r)
        //                     {
        //                         _healthService.SetWeight(LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? (double)Math.Round(weights.Kg, 2) : (double)Math.Round(weights.Lb, 2));
        //                     }
        //                 });
        //             }
        //             LoadSavedWeightFromServer();
        //             return;
        //         }
        //     }
        // };

        // //firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
        // UserDialogs.Instance.Prompt(firsttimeExercisePopup);

    }

    async void btnGetSupport_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
        }
        catch(Exception ex)
        {

        }
    }

    async void btnUpdateGoal_Clicked(System.Object sender, System.EventArgs e)
    {

        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });
            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");
            return;
        }

        CustomPromptConfig customPromptConfig = new CustomPromptConfig("Update goal weight", "Tap to enter your goal weight", AppResources.Ok,
         AppResources.Cancel,"",Keyboard.Numeric,"",7);

            customPromptConfig.ActionSelected += async (sender1, action) =>
            {
                if (action == PopupAction.OK)
                {
                    if (string.IsNullOrWhiteSpace(customPromptConfig.text) || Convert.ToDecimal(customPromptConfig.text, CultureInfo.InvariantCulture) < 1)
                    {
                        return;
                    }
                     var weightText = customPromptConfig.text.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);

                    LocalDBManager.Instance.SetDBSetting("WeightGoal", new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg.ToString().Replace(",", "."));
                    var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
                    var weights = new MultiUnityWeight(value, "kg");
                    //LblBodyweight.Text = string.Format("{0:0.00}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? weights.Kg : weights.Lb);
                    await DrMuscleRestClient.Instance.SetUserWeightGoal(new UserInfosModel()
                    {
                        WeightGoal = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value)
                    });
                    //btnUpdateGoal.IsVisible = false;
                    //btnMealPlan.IsVisible = true;

                    var userInfo = await DrMuscleRestClient.Instance.GetTargetIntake();
                    if (userInfo.TargetIntake != null)
                        LocalDBManager.Instance.SetDBSetting("TargetIntake", userInfo.TargetIntake.ToString());
                    LoadSavedWeightFromServer();
                    return;
                }
            };
            await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);

        // PromptConfig firsttimeExercisePopup = new PromptConfig()
        // {
        //     InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
        //     IsCancellable = true,
        //     Title = "Update goal weight",
        //     MaxLength = 7,
        //     Placeholder = "Tap to enter your goal weight",
        //     OkText = AppResources.Ok,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = async (weightResponse) =>
        //     {
        //         if (weightResponse.Ok)
        //         {
        //             if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
        //             {
        //                 return;
        //             }
        //             var weightText = weightResponse.Value.Replace(",", ".");
        //             decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);

        //             LocalDBManager.Instance.SetDBSetting("WeightGoal", new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg.ToString().Replace(",", "."));
        //             var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
        //             var weights = new MultiUnityWeight(value, "kg");
        //             //LblBodyweight.Text = string.Format("{0:0.00}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? weights.Kg : weights.Lb);
        //             await DrMuscleRestClient.Instance.SetUserWeightGoal(new UserInfosModel()
        //             {
        //                 WeightGoal = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value)
        //             });
        //             //btnUpdateGoal.IsVisible = false;
        //             //btnMealPlan.IsVisible = true;

        //             var userInfo = await DrMuscleRestClient.Instance.GetTargetIntake();
        //             if (userInfo.TargetIntake != null)
        //                 LocalDBManager.Instance.SetDBSetting("TargetIntake", userInfo.TargetIntake.ToString());
        //             LoadSavedWeightFromServer();
        //             return;
        //         }
        //     }
        // };

        // //firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
        // UserDialogs.Instance.Prompt(firsttimeExercisePopup);

    }

    private async void ContinueToChat(string initResponse)
    {
        //index += 1;
        //string response = initResponse;
        //var array = MealTtypes?.Split(',');
        //bool dataloaded = false;
        //UserDialogs.Instance.ShowLoading();

        //await AddQuestion("Like this plan?");

        //var btn2 = new DrMuscleButton()
        //{
        //    Text = "Change",
        //    TextColor = Color.FromHex("#195377"),
        //    BackgroundColor = Color.Transparent,
        //    HeightRequest = 55,
        //    BorderWidth = 2,
        //    BorderColor = AppThemeConstants.BlueColor,
        //    Margin = new Thickness(25, 2),
        //    CornerRadius = 0
        //};
        //btn2.Clicked += async (o, ev) =>
        //{
        //    try
        //    {
        //        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
        //    }
        //    catch (Exception ex)
        //    {

        //    }
        //    //if (App.isChangePopupSubscribed)
        //    //{
        //    //    MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "GeneralMessage");
        //    //    App.isChangePopupSubscribed = false;
        //    //}
        //    var chatPopUp = new MealGeneralPopup();
        //    chatPopUp.SetPopupTitle("Change meal plan", GeneralPopupEnum.MealPlan, "", "What changes would you like?");
        //    await PopupNavigation.Instance.PushAsync(chatPopUp);
        //    var tcs = new TaskCompletionSource<string>();
        //    try
        //    {
        //        MessagingCenter.Subscribe<Message.GeneralMessage>(this, "GeneralMessage", async (obj) =>
        //        {
        //            //App.isChangePopupSubscribed = true;
        //            if (obj.IsCanceled)
        //            {
        //                //App.BotList.Remove(App.BotList.Last());
        //                return;
        //            }
        //                if (!string.IsNullOrEmpty(obj.GeneralText))
        //                    tcs.SetResult(obj.GeneralText);

        //                string messageFromPopup = await tcs.Task;
        //                if (!string.IsNullOrEmpty(messageFromPopup) || !string.IsNullOrWhiteSpace(messageFromPopup))
        //                {
        //                    AddAnswer(messageFromPopup);
        //                    try
        //                    {
        //                        index -= 1;
        //                        string currentMealType = array?[index].TrimStart().ToLower();
        //                        var gptResponse = await AnaliesAIWithChatGPT($"{initResponse}" +
        //                            $"\r\n\r\nI need following changes in my above meal:" +
        //                            $"\r\n{messageFromPopup} for {currentMealType}\r\nShould add a header and please do not add any note, instruction or any extra things.", 0, 2000, 0, 0, 0);
        //                        //var gptResponse = await AnaliesAIWithChatGPT($"User wants following changes: {messageFromPopup} and don't remove header in {initResponse}");
        //                        //await AddQuestion($"Success! Here's your new meal plan");
        //                        await AddMealPlan(gptResponse.choices[0].message.content);
        //                        ClearOptions();
        //                        ContinueToChat(gptResponse.choices[0].message.content);
        //                        //ContinueToChat(gptResponse);
        //                    }
        //                    catch (Exception ex)
        //                    {

        //                    }
        //                }
        //        });
        //    }
        //    catch (Exception ex)
        //    {

        //    }

        //};
        //stackOptions.Children.Add(btn2);
        //if (index < array?.Length)
        //{
        //    await AddOptions("Approve", async (sender, e) =>
        //    {
        //        if(dataloaded)
        //        {
        //            ApprovedMeals += App.BotList.Select(a => a.Question).LastOrDefault();
        //            ApprovedMeals += ",";

        //            ClearOptions();
        //            await AddMealPlan(response);
        //            //AddAnswer("Save plan");
        //            //AddQuestion("Got it! Enjoy your meal plan.");
        //            ContinueToChat(response);
        //        }
        //        else
        //        {
        //            return;
        //        }
        //    });
        //}
        //else
        //{
        await AddOptions("Approve", async (sender, e) =>
        {
            //ApprovedMeals += App.BotList.Select(a => a.Question).LastOrDefault();
            //ApprovedMeals += ",";
            Console.WriteLine("Final Result = " + ApprovedMeals);
            ClearOptions();
            AddAnswer("Approve");
            //AddQuestion("Thanks! Our AI is reviewing your plan. Come back in a few minutes for full ingredients, a shopping list, and more.");
            var modalPage = new Views.GeneralPopup("lamp.png", "", "", "");
            string totalMacros = roundedProsValue + "," + roundedCarbsValue + "," + roundedFatsValue;
            modalPage = new Views.GeneralPopup("medal.png", $"Finalizing plan", $"This may take a few minutes. Learn more about your personalized plan?", "Finalizing...", null, false, false, "false", "false", "false", "false", "true", "false", "false", false, true, totalMacros);
            Config.ShowTipsNumber += 1;
            if (modalPage != null)
            {
                await PopupNavigation.Instance.PushAsync(modalPage);

                GetResultFromGPT4();
            }
            else
            {
                DisplayAlert("Error", "Try again", "OK");
             
            }
        });

        //}
        //await Task.Delay(300);
        //UserDialogs.Instance.HideLoading();
        //lstChats.ScrollTo(App.BotList.Last(), ScrollToPosition.End, false);
        //try
        //{
        //    App.IsMealReponseLoaded = true;
        //    if (index < array?.Length)
        //    {
        //        string nextMealType = array?[index].TrimStart().ToLower();
        //        switch (nextMealType)
        //        {
        //            case "lunch":
        //                dataloaded = false;
        //                response = await ProcessMealAsync(plan, LunchMinProteinGrams, LunchMinCarbsGrams, LunchMinFatGrams, LunchMinCalories, LunchMaxProteinGrams, LunchMaxCarbsGrams, LunchMaxFatGrams, LunchMaxCalories, "Lunch", index);
        //                LunchMealHeaderForGPT3 = await GetHeaderForReceipe(response);
        //                dataloaded = true;
        //                //initResponse = lunchResponse;
        //                break;

        //            case "dinner":
        //                dataloaded = false;
        //                response = await ProcessMealAsync(plan, DinnerMinProteinGrams, DinnerMinCarbsGrams, DinnerMinFatGrams, DinnerMinCalories,DinnerMaxProteinGrams, DinnerMaxCarbsGrams, DinnerMaxFatGrams, DinnerMaxCalories, "Dinner", index);
        //                DinnerMealHeaderForGPT3 = await GetHeaderForReceipe(response);
        //                dataloaded = true;
        //                //initResponse = dinnerResponse;
        //                break;

        //            case "snack 1":
        //                dataloaded = false;
        //                response = await ProcessMealAsync(plan, Snack1MinProteinGrams, Snack1MinCarbsGrams, Snack1MinFatGrams, Snack1MinCalories,  Snack1MaxProteinGrams, Snack1MaxCarbsGrams, Snack1MaxFatGrams, Snack1MaxCalories, "Snack 1", index);
        //                S1MealHeaderForGPT3 = await GetHeaderForReceipe(response);
        //                dataloaded = true;
        //                //initResponse = proteinShakeResponse;
        //                break;
        //            case "snack 2":
        //                dataloaded = false;
        //                response = await ProcessMealAsync(plan, Snack2MinProteinGrams, Snack2MinCarbsGrams, Snack2MinFatGrams, Snack2MinCalories,Snack2MaxProteinGrams, Snack2MaxCarbsGrams, Snack2MaxFatGrams, Snack2MaxCalories, "Snack 2", index);
        //                S2MealHeaderForGPT3 = await GetHeaderForReceipe(response);
        //                dataloaded = true;
        //                //initResponse = proteinShakeResponse;
        //                break;
        //            case "protein shake":
        //                dataloaded = false;
        //                response = await GetProteinShakeReceipe();
        //                dataloaded = true;
        //                //response = await ProcessMealAsync(plan, ProteinShakeProteinGrams, ProteinShakeCarbsGrams, ProteinShakeFatGrams, ProteinShakeCalories, "Protein shake", index);
        //                //initResponse = proteinShakeResponse;
        //                break;
        //            default:
        //                // Handle unknown meal type
        //                break;
        //        }
        //        switch (index)
        //        {
        //            case 1:
        //                initResponse = firstMealResponse; break;
        //            case 2:
        //                initResponse = secondMealResponse; break;
        //            case 3:
        //                initResponse = thirdMealResponse; break;
        //            case 4:
        //                initResponse = forthMealResponse; break;
        //            case 5:
        //                initResponse = fifthMealResponse; break;
        //        }
        //        currentResponse = nextMealType;
        //    }
        //}
        //catch (Exception ex)
        //{

        //}



    }

    private async Task GetResultFromGPT4(string reArrangeData = "")
    {
        if (LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value != null)
        {
            _targetIntake = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("TargetIntake").Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
        }
        _targetIntake = Math.Round(_targetIntake);
        TotalKcal = _targetIntake;
        try
        {
            var currentDaynow = currentDay;
            if (_cancellationTokenSource != null)
            {
                _cancellationTokenSource.Cancel();
            }

            _cancellationTokenSource = new CancellationTokenSource();
            CancellationToken cancellationToken = _cancellationTokenSource.Token;

            //App.BotList.Clear();
            string FinalResponseFromGPT4 = "";
            var array = App.MealTtypes.Split(',');
            var responseArray = ApprovedMeals.Split('$');
            //string FavouriteDiet = LocalDBManager.Instance.GetDBSetting("FavoriteDiet")?.Value;
            //string data = $"Final {FavouriteDiet} meal plan:" ;
            //await AddMealPlan(data, true, false, false);
            if (array?.Length >= 1)
            {
                var mealType = array?[0].TrimStart().ToLower(); // Ignore case
                string changeresult = "";
                string result = "";
                // Check if cancellation is requested
                cancellationToken.ThrowIfCancellationRequested();
                if (!string.IsNullOrEmpty(reArrangeData) && IsFirstMealChange)
                {
                    changeresult = await GetMealByGPT4(mealType, reArrangeData);

                }
                else
                {
                    bool isLastMeal = false;
                    if (array?.Length == 1)
                        isLastMeal = true;
                    result = await GetMealByGPT4(mealType, responseArray?[0], isLastMeal);

                }
                cancellationToken.ThrowIfCancellationRequested();
                //string res = await GetMealByGPT4(mealType,(!string.IsNullOrEmpty(reArrangeData) && IsFirstMealChange)? reArrangeData :responseArray?[0]);
                //string res = await GetMealByGPT4(mealType,(!string.IsNullOrEmpty(reArrangeData) && IsFirstMealChange)? reArrangeData :responseArray?[0]);

                //await AddMealPlan(res, true, true, false,false,false);
                var res = "";
                if (App.BotList != null && App.BotList.Count > 1)
                {
                    var botObj = App.BotList[1];

                    botObj.Part2 = "false";
                    botObj.Part3 = "true";
                    botObj.SetsImage = "true";
                  
                    if (!string.IsNullOrEmpty(reArrangeData) && IsFirstMealChange && !string.IsNullOrEmpty(changeresult))
                    {
                        res = changeresult;
                        FinalResponseFromGPT4 += changeresult;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = changeresult;
                        App.BotList[1] = botObj;
                        IsFirstMealChange = false;
                    }
                    else if (!IsFirstMealChange && !string.IsNullOrWhiteSpace(result))
                    {
                        res = result;
                        FinalResponseFromGPT4 += result;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = result;
                        App.BotList[1] = botObj;
                    }
                }

                if (!IsFirstMealChange)
                {
                    (decimal calories, decimal protein) cal = GetCaloriesValues(res);

                    // Check if the calories value is greater than 0
                    if (cal.calories > 0)
                    {
                        TotalKcal -= cal.calories;  // Use 'cal.calories' to access the calories value
                    }
                    if (cal.protein > 0)
                    {
                        TotalProteins += cal.protein;  // Use 'cal.protein' to access the calories value
                    }
                }

                LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDaynow, FinalResponseFromGPT4);


                //Device.BeginInvokeOnMainThread(() =>
                //{
                //    lstChats.ScrollTo(App.BotList.LastOrDefault(), ScrollToPosition.MakeVisible, false);
                //    lstChats.ScrollTo(App.BotList.LastOrDefault(), ScrollToPosition.Start, false);

                //});
            }
            if (array?.Length >= 2)
            {
                var mealType = array?[1].TrimStart().ToLower(); // Ignore case
                string changeresult = "";
                string result = "";
                cancellationToken.ThrowIfCancellationRequested();
                if (!string.IsNullOrEmpty(reArrangeData) && isSecondMealChange)
                {
                    changeresult = await GetMealByGPT4(mealType, reArrangeData);
                }
                else
                {
                    bool isLastMeal = false;
                    if (array?.Length == 2)
                        isLastMeal = true;
                    result = await GetMealByGPT4(mealType, responseArray?[1], isLastMeal);
                }
                cancellationToken.ThrowIfCancellationRequested();
                //string res = await GetMealByGPT4(mealType, (!string.IsNullOrEmpty(reArrangeData) && isSecondMealChange) ? reArrangeData : responseArray?[1]);
                //App.BotList.Remove(App.BotList.Last());
                //FinalResponseFromGPT4 += res;
                //FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                //await AddMealPlan(res, true, true, false, false, false);
                var res = "";
                if (App.BotList != null && App.BotList.Count > 2)
                {
                    var botObj = App.BotList[2];
                    botObj.Part2 = "false";
                    botObj.Part3 = "true";
                    botObj.SetsImage = "true";
                    
                    if (!string.IsNullOrEmpty(reArrangeData) && isSecondMealChange && !string.IsNullOrEmpty(changeresult))
                    {
                        res = changeresult;
                        FinalResponseFromGPT4 += changeresult;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = changeresult;
                        App.BotList[2] = botObj;
                        isSecondMealChange = false;
                    }
                    else if (!isSecondMealChange && !string.IsNullOrWhiteSpace(result))
                    {
                        res = result;
                        FinalResponseFromGPT4 += result;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = result;
                        App.BotList[2] = botObj;
                    }
                }
                if (!isSecondMealChange)
                {
                    (decimal calories, decimal protein) cal = GetCaloriesValues(res);

                    // Check if the calories value is greater than 0
                    if (cal.calories > 0)
                    {
                        TotalKcal -= cal.calories;  // Use 'cal.calories' to access the calories value
                    }
                    if (cal.protein > 0)
                    {
                        TotalProteins += cal.protein;  // Use 'cal.protein' to access the calories value
                    }
                }
                LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDaynow, FinalResponseFromGPT4);
            }
            if (array?.Length >= 3)
            {
                var mealType = array?[2].TrimStart().ToLower(); // Ignore case
                string changeresult = "";
                string result = "";
                cancellationToken.ThrowIfCancellationRequested();
                if (!string.IsNullOrEmpty(reArrangeData) && isThirdMealChange)
                {
                    changeresult = await GetMealByGPT4(mealType, reArrangeData);
                }
                else
                {
                    bool isLastMeal = false;
                    if (array?.Length == 3)
                        isLastMeal = true;
                    result = await GetMealByGPT4(mealType, responseArray?[2], isLastMeal);
                }
                cancellationToken.ThrowIfCancellationRequested();

                //string res = await GetMealByGPT4(mealType, (!string.IsNullOrEmpty(reArrangeData) && isThirdMealChange) ? reArrangeData : responseArray?[2]);
                ////string res = await GetMealByGPT4(mealType, responseArray?[2]);
                ////App.BotList.Remove(App.BotList.Last());
                //FinalResponseFromGPT4 += res;
                //FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                //await AddMealPlan(res, true, true, false, false, false);.

                var res = "";

                // Ensure the list is not null and has at least four elements before accessing index 3
                if (App.BotList != null && App.BotList.Count > 3)
                {
                    var botObj = App.BotList[3];
                    botObj.Part2 = "false";
                    botObj.Part3 = "true";
                    botObj.SetsImage = "true";
                  
                    if (!string.IsNullOrEmpty(reArrangeData) && isThirdMealChange && !string.IsNullOrEmpty(changeresult))
                    {
                        res = changeresult;
                        FinalResponseFromGPT4 += changeresult;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = changeresult;
                        App.BotList[3] = botObj;
                        isThirdMealChange = false;
                    }
                    else if (!isThirdMealChange && !string.IsNullOrWhiteSpace(result))
                    {
                        res = result;
                        FinalResponseFromGPT4 += result;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = result;
                        App.BotList[3] = botObj;
                    }
                }
                if (!isThirdMealChange)
                {
                    (decimal calories, decimal protein) cal = GetCaloriesValues(res);

                    // Check if the calories value is greater than 0
                    if (cal.calories > 0)
                    {
                        TotalKcal -= cal.calories;  // Use 'cal.calories' to access the calories value
                    }
                    if (cal.protein > 0)
                    {
                        TotalProteins += cal.protein;  // Use 'cal.protein' to access the calories value
                    }
                }
                LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDaynow, FinalResponseFromGPT4);
            }
            if (array?.Length >= 4)
            {

                var mealType = array?[3].TrimStart().ToLower(); // Ignore case
                string changeresult = "";
                string result = "";
                cancellationToken.ThrowIfCancellationRequested();
                if (!string.IsNullOrEmpty(reArrangeData) && isForthMealChange)
                {
                    changeresult = await GetMealByGPT4(mealType, reArrangeData);
                }
                else
                {
                    bool isLastMeal = false;
                    if (array?.Length == 4)
                        isLastMeal = true;
                    result = await GetMealByGPT4(mealType, responseArray?[3], isLastMeal);
                }
                cancellationToken.ThrowIfCancellationRequested();
                //string res = await GetMealByGPT4(mealType, (!string.IsNullOrEmpty(reArrangeData) && isForthMealChange) ? reArrangeData : responseArray?[3]);
                ////string res = await GetMealByGPT4(mealType, responseArray?[3]);
                ////App.BotList.Remove(App.BotList.Last());
                //FinalResponseFromGPT4 += res;
                //FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                //await AddMealPlan(res, true, true, false, false, false);
                var res = "";
                // Ensure the list is not null and has at least four elements before accessing index 4
                if (App.BotList != null && App.BotList.Count > 4)
                {
                    var botObj = App.BotList[4];
                    botObj.Part2 = "false";
                    botObj.Part3 = "true";
                    botObj.SetsImage = "true";
                    
                    if (!string.IsNullOrEmpty(reArrangeData) && isForthMealChange && !string.IsNullOrEmpty(changeresult))
                    {
                        res = changeresult;
                        FinalResponseFromGPT4 += changeresult;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = changeresult;
                        App.BotList[4] = botObj;
                        isForthMealChange = false;
                    }
                    else if (!isForthMealChange && !string.IsNullOrWhiteSpace(result))
                    {
                        res = result;
                        FinalResponseFromGPT4 += result;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = result;
                        App.BotList[4] = botObj;
                    }
                }
                if (!isForthMealChange)
                {
                    (decimal calories, decimal protein) cal = GetCaloriesValues(res);

                    // Check if the calories value is greater than 0
                    if (cal.calories > 0)
                    {
                        TotalKcal -= cal.calories;  // Use 'cal.calories' to access the calories value
                    }
                    if (cal.protein > 0)
                    {
                        TotalProteins += cal.protein;  // Use 'cal.protein' to access the calories value
                    }
                }
                LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDaynow, FinalResponseFromGPT4);
            }
            if (array?.Length >= 5)
            {
                var mealType = array?[4].TrimStart().ToLower(); // Ignore case
                string changeresult = "";
                string result = "";
                cancellationToken.ThrowIfCancellationRequested();
                if (!string.IsNullOrEmpty(reArrangeData) && isFifthMealChange)
                {
                    changeresult = await GetMealByGPT4(mealType, reArrangeData);
                }
                else
                {
                    bool isLastMeal = false;
                    if (array?.Length == 5)
                        isLastMeal = true;
                    result = await GetMealByGPT4(mealType, responseArray?[4], isLastMeal);
                }
                cancellationToken.ThrowIfCancellationRequested();
                //string res = await GetMealByGPT4(mealType, (!string.IsNullOrEmpty(reArrangeData) && isFifthMealChange) ? reArrangeData : responseArray?[4]);
                ////string res = await GetMealByGPT4(mealType, responseArray?[4]);
                ////App.BotList.Remove(App.BotList.Last());
                //FinalResponseFromGPT4 += res;
                //FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                //await AddMealPlan(res, true, true, false, false, false);
                var res = "";
                // Ensure the list is not null and has at least four elements before accessing index 5
                if (App.BotList != null && App.BotList.Count > 5)
                {
                    var botObj = App.BotList[5];
                    botObj.Part2 = "false";
                    botObj.Part3 = "true";
                    botObj.SetsImage = "true";
                    if (!string.IsNullOrEmpty(reArrangeData) && isFifthMealChange && !string.IsNullOrEmpty(changeresult))
                    {
                        res = changeresult;
                        FinalResponseFromGPT4 += changeresult;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = changeresult;
                        App.BotList[5] = botObj;
                        isFifthMealChange = false;
                    }
                    else if (!isFifthMealChange && !string.IsNullOrWhiteSpace(result))
                    {
                        res = result;
                        FinalResponseFromGPT4 += result;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = result;
                        App.BotList[5] = botObj;
                    }
                }
                if (!isFifthMealChange)
                {
                    (decimal calories, decimal protein) cal = GetCaloriesValues(res);

                    // Check if the calories value is greater than 0
                    if (cal.calories > 0)
                    {
                        TotalKcal -= cal.calories;  // Use 'cal.calories' to access the calories value
                    }
                    if (cal.protein > 0)
                    {
                        TotalProteins += cal.protein;  // Use 'cal.protein' to access the calories value
                    }
                }
                LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDaynow, FinalResponseFromGPT4);
            }
            if (array?.Length >= 6)
            {
                var mealType = array?[5].TrimStart().ToLower(); // Ignore case
                string changeresult = "";
                string result = "";
                cancellationToken.ThrowIfCancellationRequested();
                if (!string.IsNullOrEmpty(reArrangeData) && isSixthMealChange)
                {
                    changeresult = await GetMealByGPT4(mealType, reArrangeData);
                }
                else
                {
                    bool isLastMeal = false;
                    if (array?.Length == 6)
                        isLastMeal = true;
                    result = await GetMealByGPT4(mealType, responseArray?[5], isLastMeal);

                }
                cancellationToken.ThrowIfCancellationRequested();
                //string res = await GetMealByGPT4(mealType, (!string.IsNullOrEmpty(reArrangeData) && isSixthMealChange) ? reArrangeData : responseArray?[5]);
                //string res = await GetMealByGPT4(mealType, responseArray?[5]);
                //App.BotList.Remove(App.BotList.Last());
                //FinalResponseFromGPT4 += res;
                //FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                //await AddMealPlan(res, true, true, false, false, false);
                var res = "";
                // Ensure the list is not null and has at least four elements before accessing index 5
                if (App.BotList != null && App.BotList.Count > 6)
                { 
                    var botObj = App.BotList[6];
                    botObj.Part2 = "false";
                    botObj.Part3 = "true";
                    botObj.SetsImage = "true";
                    if (!string.IsNullOrEmpty(reArrangeData) && isSixthMealChange && !string.IsNullOrEmpty(changeresult))
                    {
                        res = changeresult;
                        FinalResponseFromGPT4 += changeresult;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = changeresult;
                        App.BotList[6] = botObj;
                        isSixthMealChange = false;
                    }
                    else if (!isSixthMealChange && !string.IsNullOrWhiteSpace(result))
                    {
                        res = result;
                        FinalResponseFromGPT4 += result;
                        FinalResponseFromGPT4 += "\r\n\r\nSeparate\r\n";
                        botObj.IsAdjusting = false;
                        botObj.Question = result;
                        App.BotList[6] = botObj;
                    }
                }
                if (!isSixthMealChange)
                {
                    (decimal calories, decimal protein) cal = GetCaloriesValues(res);

                    // Check if the calories value is greater than 0
                    if (cal.calories > 0)
                    {
                        TotalKcal -= cal.calories;  // Use 'cal.calories' to access the calories value
                    }
                    if (cal.protein > 0)
                    {
                        TotalProteins += cal.protein;  // Use 'cal.protein' to access the calories value
                    }
                }
                LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDaynow, FinalResponseFromGPT4);
            }

            await UpdateTotalMacrosByGPT4(FinalResponseFromGPT4, true);
            //await UpdateEditBtnOnCards();
            // await GetTotalMacrosByGPT4(FinalResponseFromGPT4);


            //await AddMealPlan(FinalResponseFromGPT4,true,true,false);



            LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDaynow, FinalResponseFromGPT4);

            Console.WriteLine("Shop list a= " + FinalResponseFromGPT4);
            string queryGroceryList = $"Generate a Shopping List" +
                                   $"\r\n\r\nConsolidate all ingredients from the recipes below into a single shopping list following these rules:" +
                                   $"\r\n\r\n1. Format Requirements:" +
                                   $"\r\n- List each ingredient with a hyphen (-)" +
                                   $"\r\n- Show only the final total quantity in parentheses" +
                                   $"\r\n- No calculations should be shown" +
                                   $"\r\n- Use this exact format:-Ingredient (total quantity unit) " +
                                   $"\r\n\r\nExample format:\r\n-Eggs (7 large)\r\n-Butter (2 tbsp)" +
                                   $"\r\n\r\n2. Measurement Rules:\r\n- Combine identical ingredients\r\n- Keep original units when possible\r\n- Never Round to whole numbers" +
                                   $"\r\n\r\nHeader must be 'Shopping List'" +
                                   $"\r\n\r\nRecipes:" +
                                   $"{FinalResponseFromGPT4}" +
                                   $"\r\n\r\nNo additional text, explanations, or calculations should be included.";
            //string queryGroceryList =
            //    $"Generate a consolidated grocery list with precise total quantities for the ingredients used in the recipes below. Ensure that quantities for the same ingredients across different recipes are accurately combined." +
            //   //$"Generate a combined grocery list with exact quantities needed for the following recipes:" +
            //   $"Pay special attention to ingredients that are mentioned multiple times, and ensure their quantities are added correctly. Thank you" +
            //   $"\r\n\r\n" +
            //   $"{FinalResponseFromGPT4}" +
            //   $"\r\n\r\n" +
            //   $"Be careful the header should be 'Shopping List'\r\n" +
            //   $"Note: Please generate a single, comprehensive grocery list that includes exact total quantities for all ingredients listed across the meals specified. Put focus on Quantities of the same ingredients should be accurately added together." +
            //   //$"Generate a single overall grocery list including exact quantities for all the ingredients listed above " +
            //   $"\r\nAnd be careful the header should be 'Shopping List' (no prose, no blank line, nothing else).";
            var groceryList = await AnaliesAIWithChatGPT(queryGroceryList, 0, 3500, 0, 0, 0);
            await AddMealPlan(groceryList.choices.FirstOrDefault()?.message.content, true, false, false, false, false, null);
            //var groceryList = await AnaliesAIWithChatGPT(queryGroceryList);
            //await AddMealPlan(groceryList.choices.FirstOrDefault()?.message.content, true, false);
            LocalDBManager.Instance.SetDBSetting($"FinalGroceryListDay" + currentDaynow, groceryList.choices.FirstOrDefault()?.message.content);

            // Combine Grocery List
            string AllGroceries = "";
            for (int h = 1; h <= 7; h++)
            {
                var grocery = LocalDBManager.Instance.GetDBSetting("FinalGroceryListDay" + h)?.Value;
                if (!string.IsNullOrEmpty(grocery))
                {
                    AllGroceries += grocery;
                    AllGroceries += "\n\n,\n\n";
                }
            }
            if (!string.IsNullOrEmpty(AllGroceries))
            {
                var query = $"Combine Shopping Lists" +
                                    $"\r\n\r\nRules for combination:" +
                                    $"\r\n\n\n1. Mathematical Precision:" +
                                    $"\r\n\r\n- Add quantities of identical ingredients" +
                                    $"\r\n\r\n2. Unit Standardization:" +
                                    $"\r\n\r\n- Keep measurements in their most practical unit" +
                                    $"\r\n\r\n- Standardize similar ingredients to the same unit" +
                                    $"\r\n\r\n- Never Round to whole numbers" +
                                    $"\r\n\r\n3. Format:" +
                                    $"\r\n\r\n-Ingredient (total quantity unit)" +
                                    $"\r\n\r\n4. Verification Steps:\r\n\r\n- Double-check all calculations\r\n- Ensure consistent units across lists\r\n- Verify no ingredients are missed" +
                                    $"\r\n\r\nShopping Lists:\r\n\r\n{AllGroceries}" +
                                        $"\n\nBe careful the header should be 'Shopping List' (no prose, no blank line, nothing else)";
                var gptResponse = await AnaliesAIWithChatGPT4o(query, 0, 2000, 0, 0, 0, "");
                string data = gptResponse.choices.FirstOrDefault()?.message.content;

                if (!string.IsNullOrEmpty(data))
                {
                    LocalDBManager.Instance.SetDBSetting("AllGroceries", data);
                }
            }
            // Combine Grocery List


            daysLabel.Text = "Day " + currentDay;
            
            App.IsMealPlanLoading = false;
            try
            {
                var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
                var planexisting = Preferences.Get($"Plan{email}", "");
                var plan = JsonConvert.DeserializeObject<DmmMealPlan>(planexisting);
                if (currentDay == plan?.DaysOnPlan)
                {
                    Preferences.Set($"IsMealPlanLoading{email}", false);
                    App.IsMealPlanLoading = false;
                }
                else if (currentDay == 1)
                {
                    Preferences.Set($"IsMealPlanLoading{email}", false);
                    App.IsMealPlanLoading = false;
                }
               
            }
            catch (Exception ex)
            {

            }
            NextArrow.IsVisible = true;
            PreviousArrow.IsVisible = true;
            
            //Satisfaction Survey
            var surveyModel = new BotModel()
            {
                StrengthImage = "survey_icon.png",
                Question = "Satisfied with meal plan?",
                Options = "MealPlan",
                Type = BotType.MealSurveyCard,
                SelectedSurveyOption = SatisfactionSurveyEnum.None
            };
            App.BotList.Add(surveyModel);

            MessagingCenter.Send<GeneralMessage>(new GeneralMessage() { GeneralText = "Loaded" }, "FinalizeMealPlan");
            
            try
            {
                if(_mainPage != null)
                {
                    _mainPage.BtnMealPlan.Text = "MEAL PLAN";
                    _mainPage.BtnMealPlan2.Text = "MEAL PLAN";
                    //_mainPage.BtnUpdateMealPlan.Text = "MEAL PLAN";
                    _mainPage.MealPlanInsideCaloriesAdjustment_.Text = "MEAL PLAN";
                    _mainPage._isMealPlanExist = true;
                }
            }
            catch (Exception ex)
            {
                
            }
        }
        catch (OperationCanceledException ex)
        {
            // Handle cancellation if needed
        }
        catch (Exception ex)
        {

        }
    }
    public (decimal calories, decimal protein) GetCaloriesValues(string response)
    {
        decimal calories = 0;
        decimal protein = 0;
        try
        {
            //string pattern = @"(\d+)\s*kcal";
            string pattern = @"(\d+(\.\d+)?)\s*kcal";
            string proteinPattern = @"(\d+(\.\d+)?)\s*g\s*protein";

            // Use Regex to find matches
            Match match = Regex.Match(response, pattern);
            Match proteinMatch = Regex.Match(response, proteinPattern);

            
            // Check if match is found
            if (match.Success)
            {
                // Output the value before 'kcal'
                calories = Convert.ToDecimal(match.Groups[1].Value);
            }
            else
            {
                calories = 0;
            }
            if (proteinMatch.Success)
            {
                protein = Convert.ToDecimal(proteinMatch.Groups[1].Value);
            }
            else{
                protein = 0;
            }
            return (calories,protein);
        }
        catch (Exception ex)
        {
            return (calories, protein);
        }
    }
    private async Task UpdateEditBtnOnCards()
    {
        string FavouriteDiet = LocalDBManager.Instance.GetDBSetting("FavoriteDiet")?.Value;
        string data = $"Final {FavouriteDiet} meal plan:";

        try
        {
            var botObj = App.BotList.Where(bot => bot.Question.Contains(data)).FirstOrDefault();
            if (botObj != null)
            {
                int index = App.BotList.IndexOf(botObj);
                for (int i = 0; i < App.BotList.Count; i++)
                {
                    var item = App.BotList[i];
                    if (item != botObj)
                    {
                        Console.WriteLine("Iteration start: " + item.Question);
                        item.SetsImage = "true";
                        App.BotList[i] = item;
                        Console.WriteLine("Iteration end");
                    }
                }
            }
        }
        catch (Exception ex)
        {

        }
        return;
    }
    private async Task UpdateTotalMacrosByGPT4(string finalResponseFromGPT4, bool equal = false)
    {
        string FavouriteDiet = LocalDBManager.Instance.GetDBSetting("FavoriteDiet")?.Value;
        Regex kcalRegex = new Regex(@"(\d+(\.\d+)?)\s+kcal");
        Regex proteinRegex = new Regex(@"(\d+(\.\d+)?)\s+g\s+protein");
        Regex carbsRegex = new Regex(@"(\d+(\.\d+)?)\s+g\s+carbs");
        Regex fatRegex = new Regex(@"(\d+(\.\d+)?)\s+g\s+fat");

        // Find matches
        MatchCollection kcalMatches = kcalRegex.Matches(finalResponseFromGPT4);
        MatchCollection proteinMatches = proteinRegex.Matches(finalResponseFromGPT4);
        MatchCollection carbsMatches = carbsRegex.Matches(finalResponseFromGPT4);
        MatchCollection fatMatches = fatRegex.Matches(finalResponseFromGPT4);

        // Calculate totals
        double totalCalories = 0;
        double totalProtein = 0;
        double totalCarbs = 0;
        double totalFat = 0;

        try
        {
            foreach (Match match in kcalMatches)
            {
                double calories;
                if (double.TryParse(match.Groups[1].Value, out calories))
                {
                    totalCalories += calories;
                }
            }

            foreach (Match match in proteinMatches)
            {
                double protein;
                if (double.TryParse(match.Groups[1].Value, out protein))
                {
                    totalProtein += protein;
                }
            }

            foreach (Match match in carbsMatches)
            {
                double carbs;
                if (double.TryParse(match.Groups[1].Value, out carbs))
                {
                    totalCarbs += carbs;
                }
            }

            foreach (Match match in fatMatches)
            {
                double fat;
                if (double.TryParse(match.Groups[1].Value, out fat))
                {
                    totalFat += fat;
                }
            }
        }
        catch (Exception ex)
        {

        }
        string data = $"Final {FavouriteDiet} meal plan:";

        try
        {
            var resverseList = App.BotList.Reverse();
            var botObj = (equal) ? resverseList?.FirstOrDefault(x => x.Question != null && (x.Question == data)) : resverseList?.FirstOrDefault(x => x.Question != null && (x.Question.Contains(data)));
            //var botObj = (equal) ? App.BotList.FirstOrDefault(x => x.Question == data) : App.BotList.FirstOrDefault(x => x.Question.Contains(data));
            // Find the index of the survey card within App.BotList
            if (botObj != null)
            {
                int index = App.BotList.IndexOf(botObj);
                var seperation = (Device.RuntimePlatform == Device.Android) ? "\r\n" : "\n";
                if (index >= 0) // Ensure botObj exists in the list before updating
                {
                    botObj.Question = $"Final {FavouriteDiet} meal plan:" +
                        $"{seperation}" +
                        $"{Math.Round(totalCalories)} kcal, {Math.Round(totalProtein)} g protein, {Math.Round(totalCarbs)} g carbs, {Math.Round(totalFat)} g fat";
                    App.BotList[index] = botObj;
                }
            }
            //bool isDifference20OrLess = Math.Abs(_targetIntake - (decimal)totalCalories) <= 20;
            //if (isDifference20OrLess)
            //    App.MealPlanCalories = (decimal)totalCalories;
            //else
            //    App.MealPlanCalories = _targetIntake;
            Macros macros = new Macros();
            macros.Calories = (decimal)totalCalories;
            macros.Protein = (decimal)totalProtein;
            macros.Carbs = (decimal)totalCarbs;
            macros.Fat = (decimal)totalFat;
            App.IsMealPlanChange = true;
            LocalDBManager.Instance.SetDBSetting("Macros", JsonConvert.SerializeObject(macros));

            //update the TargetIntake DbSetting here if the meal plan calories 20Kcal or less.
            // macros.Calories     TargetIntake

            var TargetIntake =  LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value;
            LocalDBManager.Instance.SetDBSetting("LastCaloriesValue", TargetIntake);
            LocalDBManager.Instance.SetDBSetting("LastMealCaloriesValue", macros.Calories.ToString());


        }
        catch (Exception ex)
        {

        }


        //await AddMealPlan(data, true, false, false);
    }

    private async Task GetTotalMacrosByGPT4(string finalResponseFromGPT4)
    {
        string FavouriteDiet = LocalDBManager.Instance.GetDBSetting("FavoriteDiet")?.Value;
        Regex kcalRegex = new Regex(@"(\d+(\.\d+)?)\s+kcal");
        Regex proteinRegex = new Regex(@"(\d+(\.\d+)?)\s+g\s+protein");
        Regex carbsRegex = new Regex(@"(\d+(\.\d+)?)\s+g\s+carbs");
        Regex fatRegex = new Regex(@"(\d+(\.\d+)?)\s+g\s+fat");

        // Find matches
        MatchCollection kcalMatches = kcalRegex.Matches(finalResponseFromGPT4);
        MatchCollection proteinMatches = proteinRegex.Matches(finalResponseFromGPT4);
        MatchCollection carbsMatches = carbsRegex.Matches(finalResponseFromGPT4);
        MatchCollection fatMatches = fatRegex.Matches(finalResponseFromGPT4);

        // Calculate totals
        double totalCalories = 0;
        double totalProtein = 0;
        double totalCarbs = 0;
        double totalFat = 0;

        try
        {
            foreach (Match match in kcalMatches)
            {
                double calories;
                if (double.TryParse(match.Groups[1].Value, out calories))
                {
                    totalCalories += calories;
                }
            }

            foreach (Match match in proteinMatches)
            {
                double protein;
                if (double.TryParse(match.Groups[1].Value, out protein))
                {
                    totalProtein += protein;
                }
            }

            foreach (Match match in carbsMatches)
            {
                double carbs;
                if (double.TryParse(match.Groups[1].Value, out carbs))
                {
                    totalCarbs += carbs;
                }
            }

            foreach (Match match in fatMatches)
            {
                double fat;
                if (double.TryParse(match.Groups[1].Value, out fat))
                {
                    totalFat += fat;
                }
            }
        }
        catch (Exception ex)
        {

        }
        var seperation = (Device.RuntimePlatform == Device.Android) ? "\r\n" : "\n";
        string data = $"Final {FavouriteDiet} meal plan:" +
            $"{seperation}" +
            $"{Math.Round(totalCalories)} kcal, {Math.Round(totalProtein)} g protein, {Math.Round(totalCarbs)} g carbs, {Math.Round(totalFat)} g fat";

        //var TargetIntake =  LocalDBManager.Instance.GetDBSetting("TargetIntake")?.Value;
        //LocalDBManager.Instance.SetDBSetting("LastCaloriesValue", TargetIntake);

        await AddMealPlan(data, true, true, false, false, false, null);
    }


    private double GetCaloriesFromMeal(string finalResponseFromGPT4)
    {
        Regex kcalRegex = new Regex(@"(\d+(\.\d+)?)\s+kcal");
       
        // Find matches
        MatchCollection kcalMatches = kcalRegex.Matches(finalResponseFromGPT4);
 
        // Calculate totals
        double totalCalories = 0;

        try
        {
            foreach (Match match in kcalMatches)
            {
                double calories;
                if (double.TryParse(match.Groups[1].Value, out calories))
                {
                    totalCalories += calories;
                }
            }

        }
        catch (System.Exception ex)
        {

        }
        return totalCalories;
    }

    private async Task<string> GetMealByGPT4(string mealType, string responseByGPT3, bool isLastMeal = false)
    {
        string response = "";
        try
        {
            switch (mealType)
            {
                case "breakfast":
                    response = await ProcessMealWithGPT4(mealType, App.BreakfastMinProteinGrams, App.BreakfastMinCarbsGrams, App.BreakfastMinFatGrams, App.BreakfastMinCalories, App.BreakfastMaxProteinGrams, App.BreakfastMaxCarbsGrams, App.BreakfastMaxFatGrams, App.BreakfastMaxCalories, responseByGPT3, BFMealHeaderForGPT3, 0, isLastMeal);
                    break;
                case "lunch":
                    response = await ProcessMealWithGPT4(mealType, App.LunchMinProteinGrams, App.LunchMinCarbsGrams, App.LunchMinFatGrams, App.LunchMinCalories, App.LunchMaxProteinGrams, App.LunchMaxCarbsGrams, App.LunchMaxFatGrams, App.LunchMaxCalories, responseByGPT3, LunchMealHeaderForGPT3, 0, isLastMeal);
                    break;
                case "dinner":
                    response = await ProcessMealWithGPT4(mealType, App.DinnerMinProteinGrams, App.DinnerMinCarbsGrams, App.DinnerMinFatGrams, App.DinnerMinCalories, App.DinnerMaxProteinGrams, App.DinnerMaxCarbsGrams, App.DinnerMaxFatGrams, App.DinnerMaxCalories, responseByGPT3, DinnerMealHeaderForGPT3, 0, isLastMeal);
                    break;
                case "snack 1":
                    response = await ProcessMealWithGPT4(mealType, App.Snack1MinProteinGrams, App.Snack1MinCarbsGrams, App.Snack1MinFatGrams, App.Snack1MinCalories, App.Snack1MaxProteinGrams, App.Snack1MaxCarbsGrams, App.Snack1MaxFatGrams, App.Snack1MaxCalories, responseByGPT3, S1MealHeaderForGPT3, 0, isLastMeal);
                    break;
                case "snack 2":
                    response = await ProcessMealWithGPT4(mealType, App.Snack2MinProteinGrams, App.Snack2MinCarbsGrams, App.Snack2MinFatGrams, App.Snack2MinCalories, App.Snack2MaxProteinGrams, App.Snack2MaxCarbsGrams, App.Snack2MaxFatGrams, App.Snack2MaxCalories, responseByGPT3, S2MealHeaderForGPT3, 0, isLastMeal);
                    break;
                case "protein shake":
                    int scoopValue = 1;
                    try
                    {
                        string pattern = @"(\d+) scoop(s?)";
                        MatchCollection matches = Regex.Matches(responseByGPT3, pattern);
                        if (matches.Count == 0)
                        {
                            scoopValue = 1;
                        }
                        foreach (Match match in matches)
                        {
                            scoopValue = Convert.ToInt32(match.Groups[1].Value);
                        }
                    }
                    catch (Exception ex)
                    {
                        scoopValue = 1;
                    }

                    App.ProteinShakeProteinGrams = App.ProteinShakeProteinGrams * scoopValue;
                    App.ProteinShakeCalories = App.ProteinShakeProteinGrams * 4;
                    //var proteinMeal = await GetProteinShakeReceipe();

                    string carbsFats = await GetProteinShakesCarbsFats(responseByGPT3);
                    if (!string.IsNullOrEmpty(carbsFats))
                    {
                        var splitedValue = carbsFats.Split(',');
                        if (splitedValue?.Length >= 2)
                        {
                            App.ProteinShakeCarbsGrams = Convert.ToDecimal(splitedValue[0]);
                            App.ProteinShakeFatGrams = Convert.ToDecimal(splitedValue[1]);
                            App.ProteinShakeCalories = App.ProteinShakeCalories + (App.ProteinShakeCarbsGrams * 4) + (App.ProteinShakeFatGrams * 9);
                        }
                    }
                    else
                    {
                        App.ProteinShakeCarbsGrams = 3;
                        App.ProteinShakeFatGrams = 4;
                    }
                    response = await ProcessMealWithGPT4(mealType, App.ProteinShakeProteinGrams, App.ProteinShakeCarbsGrams, App.ProteinShakeFatGrams, App.ProteinShakeCalories, 0, 0, 0, 0, responseByGPT3, "Proten hake:", 0);
                    //response = await GetProteinShakeReceipeGPT4();
                    break;
            }
            string convertedData = ConvertToFinalData(response, mealType);
            var value = GetCaloriesValues(convertedData);

            if ((!string.IsNullOrEmpty(convertedData) && (convertedData.Contains("...")) || value.calories == 0 || convertedData == ""))
            {
                convertedData = await GetMealByGPT4(mealType, responseByGPT3);
            }
            convertedData = await RefineGPT4Data(convertedData);
            return convertedData;
        }
        catch (Exception ex)
        {
            return "";
        }
    }
    private async Task<string> RefineGPT4Data(string data)
    {
        try
        {
            string searchString = "Protein kcal";
            string lowerInput = data.ToLower();
            string lowerSearchString = searchString.ToLower();

            int index = lowerInput.IndexOf(lowerSearchString);

            if (index >= 0)
            {
                data = data.Substring(0, index);
            }
        }
        catch (Exception ex)
        {

        }
        return data;
    }
    private async Task<string> GetProteinShakesCarbsFats(string responseByGPT3)
    {
        string carbsFats = "";
        try
        {
            var res = await GetChatGPT4("You are an expert I need only total exact carbs and exact fats value. " +
                "\r\nIf you cannot find the specific value for the milk, make an assumption of small cup of milk." +
                "\r\nFormat your response as follow (no prose, no blank line)" +
                "\r\ncarbs : xx " + 
                "\r\nfats : xx" + 
                "\r\nFor this meal: \r\n" + responseByGPT3);
            Console.WriteLine("carbs & fats re = " + res.choices.FirstOrDefault()?.message.content);

            string pattern = @"\d+";

            // Match numeric values using regular expression
            MatchCollection matches = Regex.Matches(res.choices.FirstOrDefault()?.message.content, pattern);

            // Extract and print the numeric values
            foreach (Match match in matches)
            {
                carbsFats += match.Value;
                carbsFats += ",";
            }
        }
        catch (Exception ex)
        {
            await GetProteinShakesCarbsFats(responseByGPT3);
        }
        return carbsFats;
    }


    private async Task<ChatGPTResponse> AnaliesAIWithChatGPT(string query, double temperature = 0.2, int maxTokens = 2000, double topP = 0.5, double frequencyPenalty = 0.5, double presencePenalty = 0.5)
    {
        try
        {
            string openaiKey = AppThemeConstants.GPTKey;

            string prompt = query;
            using (var _httpClient = new HttpClient())
            {
                _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", openaiKey);
                //string model = "gpt-4-1106-preview";//"text-davinci-002"; // model for GPT-3.5 Turbo
                //string model = "gpt-4o";//"text-davinci-002"; // model for GPT-3.5 Turbo
                string model = "gpt-4o";//"text-davinci-002"; // model for GPT-3.5 Turbo
                var requestUrl = "https://api.openai.com/v1/chat/completions";

                var requestBody = new
                {
                    messages = new[] { new { role = "user", content = prompt } },
                    model = model,
                    temperature = temperature,
                    max_tokens = maxTokens,
                    top_p = topP,
                    frequency_penalty = frequencyPenalty,
                    presence_penalty = presencePenalty
                };
                var requestBodyJson = Newtonsoft.Json.JsonConvert.SerializeObject(requestBody);
                var requestContent = new StringContent(requestBodyJson, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(requestUrl, requestContent);
                var responseBodyJson = await response.Content.ReadAsStringAsync();
                var compilationResponse = System.Text.Json.JsonSerializer.Deserialize<ChatGPTResponse>(responseBodyJson);
                //App.BotList.Remove(App.BotList.Last());
                return compilationResponse;
            }
        }
        catch (System.Exception ex)
        {
            return default;
        }

    }
    private async Task<ChatGPTResponse> AnaliesAIWithChatGPT4o(string query, double temperature = 0, int maxTokens = 2000, double topP = 0, double frequencyPenalty = 0, double presencePenalty = 0,string assistant = "",string ApprovedMeals="")
    {
        try
        {
            if (!string.IsNullOrEmpty(ApprovedMeals) && string.IsNullOrEmpty(assistant))
            {
                assistant = $"You are a helpful assistant. Ensure no meal is similar to any meal from the previous plans listed below. A meal is considered similar if it have same name, repeats the same combination of ingredients, similar cooking methods, or closely matches the macros of any previous meal.\r\n\r\nPrevious Plans: :\n\n {ApprovedMeals}";
            }
            else if (!string.IsNullOrEmpty(assistant))
            {
                assistant = $"You are a helpful assistant. Ensure no meal is similar to any meal from the previous plans listed below. A meal is considered similar if it have same name, repeats the same combination of ingredients, similar cooking methods, or closely matches the macros of any previous meal.\r\n\r\nPrevious Plans: :\n\n {assistant}\r\n{ApprovedMeals}";
            }
           
            string openaiKey = AppThemeConstants.GPTKey;
            string prompt = query;

            var messageList = !string.IsNullOrEmpty(assistant)
                            ? new[] { new { role = "user", content = prompt }, new { role = "system", content = assistant } }
                            : new[] { new { role = "user", content = prompt } };
            using (var _httpClient = new HttpClient())
            {
                _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", openaiKey);
                //string model = "gpt-4-1106-preview";//"text-davinci-002"; // model for GPT-3.5 Turbo
                string model = "gpt-4o";//"text-davinci-002"; // model for GPT-3.5 Turbo
                                        //string model = "gpt-3.5-turbo";//"text-davinci-002"; // model for GPT-3.5 Turbo
                var requestUrl = "https://api.openai.com/v1/chat/completions";

                var requestBody = new
                {
                    messages = messageList,
                    model = model,
                    temperature = temperature,
                    max_tokens = maxTokens,
                    top_p = topP,
                    frequency_penalty = frequencyPenalty,
                    presence_penalty = presencePenalty
                };
                var requestBodyJson = Newtonsoft.Json.JsonConvert.SerializeObject(requestBody);
                var requestContent = new StringContent(requestBodyJson, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(requestUrl, requestContent);
                var responseBodyJson = await response.Content.ReadAsStringAsync();
                var compilationResponse = System.Text.Json.JsonSerializer.Deserialize<ChatGPTResponse>(responseBodyJson);
                //App.BotList.Remove(App.BotList.Last());
                return compilationResponse;
            }
        }
        catch (System.Exception ex)
        {
            return default;
        }

    }
    private async Task<ChatGPTResponse> GetChatGPT4(string query, double temperature = 0, int maxTokens = 4000, double topP = 0, double frequencyPenalty = 0, double presencePenalty = 0)
    {
        try
        {
            string openaiKey = AppThemeConstants.GPTKey;
            string prompt = query;
            using (var _httpClient = new HttpClient())
            {
                _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", openaiKey);
                string model = "gpt-4o";
                //string model = "gpt-4-1106-preview";
                //string model = "gpt-3.5-turbo";//"text-davinci-002"; // model for GPT-3.5 Turbo
                var requestUrl = "https://api.openai.com/v1/chat/completions";

                var requestBody = new
                {
                    messages = new[] { new { role = "user", content = prompt } },
                    model = model,
                    temperature = temperature,
                    max_tokens = maxTokens,
                    top_p = topP,
                    frequency_penalty = frequencyPenalty,
                    presence_penalty = presencePenalty
                };
                var requestBodyJson = Newtonsoft.Json.JsonConvert.SerializeObject(requestBody);
                var requestContent = new StringContent(requestBodyJson, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(requestUrl, requestContent);
                var responseBodyJson = await response.Content.ReadAsStringAsync();
                var compilationResponse = System.Text.Json.JsonSerializer.Deserialize<ChatGPTResponse>(responseBodyJson);
                //App.BotList.Remove(App.BotList.Last());
                return compilationResponse;
            }
        }
        catch (System.Exception ex)
        {
            return default;
        }

    }

    private void PreviousDay(object sender, EventArgs e)
    {
        if (App.IsMealPlanLoading)
            return;
        if (currentDay == 7 && daysLabel.Text == "Shopping List")
        {
            currentDay = 1;
        }
        if (currentDay == 1 && daysLabel.Text != "Shopping List")
        {
            GetCombinedGrocerryList();
        }
        else
        {
            currentDay = currentDay == 1 ? 7 : currentDay - 1; // Wrap around to Day 3 if at Day 1
            daysLabel.Text = "Day " + currentDay;

            var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
            var planexisting = Preferences.Get($"Plan{email}", "");
            bool isMealPlanLoading = Preferences.Get($"IsMealPlanLoading{email}", false);
            if (isMealPlanLoading == true)
            {
                App.plan = JsonConvert.DeserializeObject<DmmMealPlan>(planexisting);
                if (App.plan.DaysOnPlan == currentDay)
                {
                    object sender1 = this;
                    EventArgs e1 = EventArgs.Empty;
                    GenerateNewDayMeal(sender1, e1);
                }
            }
            else
            {
                if (currentDay == 1)
                {
                    GenerateBtnsStack.IsVisible = false;
                }
                else
                {
                    GenerateBtnsStack.IsVisible = true;
                }
                ChangePlanByDay(currentDay);
            }
        }
    }
    public async void GetCombinedGrocerryList()
    {
        try
        {
            daysLabel.Text = "Shopping List";
            GenerateBtnsStack.IsVisible = false;
            MainGrid.IsVisible = true;
            App.BotList.Clear();
            NextArrow.IsVisible = false;
            PreviousArrow.IsVisible = false;
            var existingGroceries = LocalDBManager.Instance.GetDBSetting("AllGroceries")?.Value;
            if (!string.IsNullOrEmpty(existingGroceries))
            {
                await AddMealPlan(existingGroceries, false, true, false, false, false, null);
                var surveyModel = new BotModel()
                {
                    StrengthImage = "survey_icon.png",
                    Question = "Satisfied with meal plan?",
                    Options = "MealPlan",
                    Type = BotType.MealSurveyCard,
                    SelectedSurveyOption = SatisfactionSurveyEnum.None
                };
                App.BotList.Add(surveyModel);
            }
            else
            {
                
                string AllGroceries = "";
                for (int h = 1; h <= 7; h++)
                {
                    var grocery = LocalDBManager.Instance.GetDBSetting("FinalGroceryListDay" + h)?.Value;
                    if (!string.IsNullOrEmpty(grocery))
                    {
                        AllGroceries += grocery;
                        AllGroceries += "\n\n,\n\n";
                    }
                }
                if (!string.IsNullOrEmpty(AllGroceries))
                {
                    var query = $"Combine Shopping Lists" +
                                    $"\r\n\r\nRules for combination:" +
                                    $"\r\n\n\n1. Mathematical Precision:" +
                                    $"\r\n\r\n- Add quantities of identical ingredients" +
                                    $"\r\n\r\n2. Unit Standardization:" +
                                    $"\r\n\r\n- Keep measurements in their most practical unit" +
                                    $"\r\n\r\n- Standardize similar ingredients to the same unit" +
                                    $"\r\n\r\n- Never Round to whole numbers" +
                                    $"\r\n\r\n3. Format:" +
                                    $"\r\n\r\n-Ingredient (total quantity unit)" +
                                    $"\r\n\r\n4. Verification Steps:\r\n\r\n- Double-check all calculations\r\n- Ensure consistent units across lists\r\n- Verify no ingredients are missed" +
                                    $"\r\n\r\nShopping Lists:\r\n\r\n{AllGroceries}" +
                                        $"\n\nBe careful the header should be 'Shopping List' (no prose, no blank line, nothing else)";
                    var gptResponse = await AnaliesAIWithChatGPT4o(query, 0, 2000, 0, 0, 0, "");
                    string data = gptResponse.choices.FirstOrDefault()?.message.content;

                    if (!string.IsNullOrEmpty(data))
                    {
                        LocalDBManager.Instance.SetDBSetting("AllGroceries", data);
                        await AddMealPlan(data, false, true, false, false, false, null);
                        var surveyModel = new BotModel()
                        {
                            StrengthImage = "survey_icon.png",
                            Question = "Satisfied with meal plan?",
                            Options = "MealPlan",
                            Type = BotType.MealSurveyCard,
                            SelectedSurveyOption = SatisfactionSurveyEnum.None
                        };
                        App.BotList.Add(surveyModel);
                    }
                }
            }
            
        }
        catch (Exception ex)
        {
        }
        finally
        {
            NextArrow.IsVisible = true;
            PreviousArrow.IsVisible = true;
        }
    }
    private async void NextDay(object sender, EventArgs e)
    {
        if (App.IsMealPlanLoading)
            return;
        if(currentDay == 1 && daysLabel.Text == "Shopping List")
        {
            currentDay = 7;
        }
        if(currentDay == 7 && daysLabel.Text != "Shopping List")
        {
            GetCombinedGrocerryList();
        }
        else
        {
            currentDay = (currentDay % 7) + 1; // Wrap around to 1 after Day 3
            daysLabel.Text = "Day " + currentDay;
            var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
            var planexisting = Preferences.Get($"Plan{email}", "");
            bool isMealPlanLoading = Preferences.Get($"IsMealPlanLoading{email}", false);
            string MealPlanDay = "";
            try
            {
                var mealDay = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + currentDay);
                if(mealDay != null)
                    MealPlanDay = mealDay?.Value ?? "";
            }
            catch (Exception ex)
            {

            }
            double lastCal = 0;

            var lastCalr = LocalDBManager.Instance.GetDBSetting("LastMealCaloriesValue");
            if (lastCalr != null)
            {
                lastCal = Convert.ToDouble(lastCalr?.Value);

            }

            double calory = 0;
            double difference = 0;
            if (!string.IsNullOrEmpty(MealPlanDay)) 
            { 
                try
                {
                    if (!string.IsNullOrEmpty(MealPlanDay))
                    {
                        calory = GetCaloriesFromMeal(MealPlanDay);
                    }
                    try
                    {
                        if (lastCal > 0 && calory > lastCal)
                        {
                            difference = Convert.ToDouble(calory) - lastCal;
                        }
                        else if (lastCal > 0 && lastCal > calory)
                        {
                            difference = lastCal - Convert.ToDouble(calory);
                        }
                    }
                    catch (Exception ex)
                    {

                    }

                }
                catch (Exception ex)
                {

                }
            }

            if (isMealPlanLoading == true)
            {
                App.plan = JsonConvert.DeserializeObject<DmmMealPlan>(planexisting);
                if (App.plan.DaysOnPlan == currentDay)
                {
                    object sender1 = this;
                    EventArgs e1 = EventArgs.Empty;
                    GenerateNewDayMeal(sender1, e1);
                }
            }
            else if (difference >= 50)
            {
                try
                {
                    var plann = JsonConvert.DeserializeObject<DmmMealPlan>(planexisting);
                    App.plan = plann;

                    object sender1 = this;
                    EventArgs e1 = EventArgs.Empty;
                    GenerateNewDayMeal(sender1, e1);
                }
                catch (Exception ex)
                {

                }
                
            }
            else
            {
                if (currentDay == 1)
                {
                    GenerateBtnsStack.IsVisible = false;
                }
                else
                {
                    GenerateBtnsStack.IsVisible = true;
                }
                ChangePlanByDay(currentDay);
            }
        }
        

    }
    private async void GenerateNewDayMeal(object sender, EventArgs e)
    {
        var macrosDistribution = LocalDBManager.Instance.GetDBSetting("MacrosDistribution")?.Value;

        if (!string.IsNullOrEmpty(macrosDistribution))
        {
            var obj = JsonConvert.DeserializeObject<MacrosDistribution>(macrosDistribution);
            await CalculateMacros(obj.TargetIntake, obj.MinPro, obj.MaxPro, obj.MinCarb, obj.MaxCarb, obj.MinFat, obj.MaxFat);
            MainGrid.IsVisible = true;
            GenerateBtnsStack.IsVisible = false;
            App.minPro = obj.MinPro;
            App.maxPro = obj.MaxPro;
            App.minCarb = obj.MinCarb;
            App.maxCarb = obj.MaxCarb;
            App.minFat = obj.MinFat;
            App.maxFat = obj.MaxFat;

            App.BreakfastMinCalories = obj.BreakfastMinCalories;
            App.BreakfastMaxCalories = obj.BreakfastMaxCalories;
            App.BreakfastMinProteinGrams = obj.BreakfastMinProteinGrams;
            App.BreakfastMaxProteinGrams = obj.BreakfastMaxProteinGrams;
            App.BreakfastMinCarbsGrams = obj.BreakfastMinCarbsGrams;
            App.BreakfastMaxCarbsGrams = obj.BreakfastMaxCarbsGrams;
            App.BreakfastMinFatGrams = obj.BreakfastMinFatGrams;
            App.BreakfastMaxFatGrams = obj.BreakfastMaxFatGrams;
            
            App.LunchMinCalories = obj.LunchMinCalories;
            App.LunchMaxCalories = obj.LunchMaxCalories;
            App.LunchMinProteinGrams = obj.LunchMinProteinGrams;
            App.LunchMaxProteinGrams = obj.LunchMaxProteinGrams;
            App.LunchMinCarbsGrams = obj.LunchMinCarbsGrams;
            App.LunchMaxCarbsGrams = obj.LunchMaxCarbsGrams;
            App.LunchMinFatGrams = obj.LunchMinFatGrams;
            App.LunchMaxFatGrams = obj.LunchMaxFatGrams;
            
            App.DinnerMinCalories = obj.DinnerMinCalories;
            App.DinnerMaxCalories = obj.DinnerMaxCalories;
            App.DinnerMinProteinGrams = obj.DinnerMinProteinGrams;
            App.DinnerMaxProteinGrams = obj.DinnerMaxProteinGrams;
            App.DinnerMinCarbsGrams = obj.DinnerMinCarbsGrams;
            App.DinnerMaxCarbsGrams = obj.DinnerMaxCarbsGrams;
            App.DinnerMinFatGrams = obj.DinnerMinFatGrams;
            App.DinnerMaxFatGrams = obj.DinnerMaxFatGrams;
            
            App.ProteinShakeCalories = obj.ProteinShakeCalories;
            App.ProteinShakeProteinGrams = obj.ProteinShakeProteinGrams;
            App.ProteinShakeCarbsGrams = obj.ProteinShakeCarbsGrams;
            App.ProteinShakeFatGrams = obj.ProteinShakeFatGrams;
            
            App.Snack1MinCalories = obj.Snack1MinCalories;
            App.Snack1MaxCalories = obj.Snack1MaxCalories;
            App.Snack1MinProteinGrams = obj.Snack1MinProteinGrams;
            App.Snack1MaxProteinGrams = obj.Snack1MaxProteinGrams;
            App.Snack1MinCarbsGrams = obj.Snack1MinCarbsGrams;
            App.Snack1MaxCarbsGrams = obj.Snack1MaxCarbsGrams;
            App.Snack1MinFatGrams = obj.Snack1MinFatGrams;
            App.Snack1MaxFatGrams = obj.Snack1MaxFatGrams;
            
            App.Snack2MinCalories = obj.Snack2MinCalories;
            App.Snack2MaxCalories = obj.Snack2MaxCalories;
            App.Snack2MinProteinGrams = obj.Snack2MinProteinGrams;
            App.Snack2MaxProteinGrams = obj.Snack2MaxProteinGrams;
            App.Snack2MinCarbsGrams = obj.Snack2MinCarbsGrams;
            App.Snack2MaxCarbsGrams = obj.Snack2MaxCarbsGrams;
            App.Snack2MinFatGrams = obj.Snack2MinFatGrams;
            App.Snack2MaxFatGrams = obj.Snack2MaxFatGrams;
            SyncWithServer(true);
        }
    }

    private async void CopyFromPrevious(object sender, EventArgs e)
    {
        try
        {
            PreviousArrow.IsVisible = false;
            NextArrow.IsVisible = false;
            int previousDay = currentDay == 1 ? 7 : currentDay - 1;
            var finalMealPlan = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + previousDay)?.Value;
            if (!string.IsNullOrEmpty(finalMealPlan))
            {
                LocalDBManager.Instance.SetDBSetting("AllGroceries", "");
                LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDay, finalMealPlan);
                var FinalGroceryListDay = LocalDBManager.Instance.GetDBSetting($"FinalGroceryListDay" + previousDay)?.Value;
                if (!string.IsNullOrEmpty(FinalGroceryListDay))
                {
                    LocalDBManager.Instance.SetDBSetting($"FinalGroceryListDay" + currentDay, FinalGroceryListDay);

                }
            }
            else
            {

                var ShowPopUp = await HelperClass.DisplayCustomPopup("Load from day 1?","No meal on previous day, load from day 1.",
                "Load day 1","Cancel ");
                    ShowPopUp.ActionSelected += async (sender,action) => {

                            if (action == Views.PopupAction.OK)
                            {
                                LocalDBManager.Instance.SetDBSetting("AllGroceries", "");
                                var finalMealPlan1 = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay1")?.Value;
                                if (!string.IsNullOrEmpty(finalMealPlan1))
                                {
                                    LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDay, finalMealPlan1);
                                }
                                var FinalGroceryListDay = LocalDBManager.Instance.GetDBSetting($"FinalGroceryListDay1")?.Value;
                                if (!string.IsNullOrEmpty(FinalGroceryListDay))
                                {
                                    LocalDBManager.Instance.SetDBSetting($"FinalGroceryListDay" + currentDay, FinalGroceryListDay);
                                }
                            }
                            
                    };
                    
                // ConfirmConfig confirm = new ConfirmConfig()
                // {
                //     Title = "Load from day 1?",
                //     Message = "No meal on previous day, load from day 1.",
                //     OkText = "Load day 1",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     CancelText = "Cancel ",
                // };

                // var x = await UserDialogs.Instance.ConfirmAsync(confirm);
                // if (x)
                // {
                //     LocalDBManager.Instance.SetDBSetting("AllGroceries", "");
                //     var finalMealPlan1 = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay1")?.Value;
                //     if (!string.IsNullOrEmpty(finalMealPlan1))
                //     {
                //         LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDay, finalMealPlan1);
                //     }
                //     var FinalGroceryListDay = LocalDBManager.Instance.GetDBSetting($"FinalGroceryListDay1")?.Value;
                //     if (!string.IsNullOrEmpty(FinalGroceryListDay))
                //     {
                //         LocalDBManager.Instance.SetDBSetting($"FinalGroceryListDay" + currentDay, FinalGroceryListDay);
                //     }

                // }
            }

            ChangePlanByDay(currentDay);

            // Combine Grocery List
            string AllGroceries = "";
            for (int h = 1; h <= 7; h++)
            {
                var grocery = LocalDBManager.Instance.GetDBSetting("FinalGroceryListDay" + h)?.Value;
                if (!string.IsNullOrEmpty(grocery))
                {
                    AllGroceries += grocery;
                    AllGroceries += "\n\n,\n\n";
                }
            }
            if (!string.IsNullOrEmpty(AllGroceries))
            {
                var query = $"Combine Shopping Lists" +
                                    $"\r\n\r\nRules for combination:" +
                                    $"\r\n\n\n1. Mathematical Precision:" +
                                    $"\r\n\r\n- Add quantities of identical ingredients" +
                                    $"\r\n\r\n2. Unit Standardization:" +
                                    $"\r\n\r\n- Keep measurements in their most practical unit" +
                                    $"\r\n\r\n- Standardize similar ingredients to the same unit" +
                                    $"\r\n\r\n- Never Round to whole numbers" +
                                    $"\r\n\r\n3. Format:" +
                                    $"\r\n\r\n-Ingredient (total quantity unit)" +
                                    $"\r\n\r\n4. Verification Steps:\r\n\r\n- Double-check all calculations\r\n- Ensure consistent units across lists\r\n- Verify no ingredients are missed" +
                                    $"\r\n\r\nShopping Lists:\r\n\r\n{AllGroceries}" +
                                        $"\n\nBe careful the header should be 'Shopping List' (no prose, no blank line, nothing else)";
                var gptResponse = await AnaliesAIWithChatGPT4o(query, 0, 2000, 0, 0, 0, "");
                string data = gptResponse.choices.FirstOrDefault()?.message.content;

                if (!string.IsNullOrEmpty(data))
                {
                    LocalDBManager.Instance.SetDBSetting("AllGroceries", data);
                }
            }
        }
        catch (Exception ex)
        {

        }
        finally
        {
            PreviousArrow.IsVisible = true;
            NextArrow.IsVisible = true;
        }
        // Combine Grocery List
    }
    private SemaphoreSlim semaphoreSlim = new SemaphoreSlim(1, 1);
    private async void ChangePlanByDay(int currentDay)
    {
        GenerateBtnsStack.IsVisible = false;
        // Wait until the semaphore is available
        await semaphoreSlim.WaitAsync();


        try
        {
            var MealPlanDay = LocalDBManager.Instance.GetDBSetting($"FinalMealPlanDay" + currentDay)?.Value;
            if (!string.IsNullOrEmpty(MealPlanDay))
            {
                string[] sections1 = MealPlanDay.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries).Where(s => !string.IsNullOrWhiteSpace(s)).ToArray();
                if (sections1?.Length < 4)
                {
                    MealPlanDay = "";
                    LocalDBManager.Instance.SetDBSetting($"FinalMealPlanDay" + currentDay, "");
                }
            }
                
            if (!string.IsNullOrEmpty(MealPlanDay))
            {
                MainGrid.IsVisible = true;
                GenerateBtnsStack.IsVisible = false;
                App.BotList?.Clear();
                await GetTotalMacrosByGPT4(MealPlanDay);
                string[] sections = MealPlanDay.Split(new string[] { "Separate" }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string section in sections)
                {
                    if (!string.IsNullOrEmpty(section) && !string.IsNullOrWhiteSpace(section))
                    {
                        await AddMealPlan(section.Trim(), false, true, false, false, true, false);
                    }
                }
                var grocery = LocalDBManager.Instance.GetDBSetting("FinalGroceryListDay" + currentDay)?.Value;
                if (!string.IsNullOrEmpty(grocery))
                {
                    await AddMealPlan(grocery, false, true, false, false, false, null);
                }
                if (App.BotList.LastOrDefault().Type != BotType.MealSurveyCard)
                {
                    var surveyModel = new BotModel()
                    {
                        StrengthImage = "survey_icon.png",
                        Question = "Satisfied with meal plan?",
                        Options = "MealPlan",
                        Type = BotType.MealSurveyCard,
                        SelectedSurveyOption = SatisfactionSurveyEnum.None
                    };
                    App.BotList.Add(surveyModel);
                }

            }
            else
            {
                App.BotList?.Clear();
                MainGrid.IsVisible = false;

                GenerateBtnsStack.IsVisible = true;
            }
        }
        catch (Exception ex)
        {

        }
        finally
        {

            // Release the semaphore
            semaphoreSlim.Release();
        }
    }
}
public class MacrosDistribution
{
    public decimal TargetIntake { get; set; }
    public int MinPro { get; set; }
    public int MaxPro { get; set; }
    public int MinCarb { get; set; }
    public int MaxCarb { get; set; }
    public int MinFat { get; set; }
    public int MaxFat { get; set; }
    public decimal BreakfastMinCalories { get; set; }
    public decimal BreakfastMaxCalories { get; set; }
    public decimal BreakfastMinProteinGrams { get; set; }
    public decimal BreakfastMaxProteinGrams { get; set; }
    public decimal BreakfastMinCarbsGrams { get; set; }
    public decimal BreakfastMaxCarbsGrams { get; set; }
    public decimal BreakfastMinFatGrams { get; set; }
    public decimal BreakfastMaxFatGrams { get; set; }

    public decimal LunchMinCalories { get; set; }
    public decimal LunchMaxCalories { get; set; }
    public decimal LunchMinProteinGrams { get; set; }
    public decimal LunchMaxProteinGrams { get; set; }
    public decimal LunchMinCarbsGrams { get; set; }
    public decimal LunchMaxCarbsGrams { get; set; }
    public decimal LunchMinFatGrams { get; set; }
    public decimal LunchMaxFatGrams { get; set; }

    public decimal DinnerMinCalories { get; set; }
    public decimal DinnerMaxCalories { get; set; }
    public decimal DinnerMinProteinGrams { get; set; }
    public decimal DinnerMaxProteinGrams { get; set; }
    public decimal DinnerMinCarbsGrams { get; set; }
    public decimal DinnerMaxCarbsGrams { get; set; }
    public decimal DinnerMinFatGrams { get; set; }
    public decimal DinnerMaxFatGrams { get; set; }

    public decimal ProteinShakeCalories { get; set; }
    public decimal ProteinShakeProteinGrams { get; set; }
    public decimal ProteinShakeCarbsGrams { get; set; }
    public decimal ProteinShakeFatGrams { get; set; }

    public decimal Snack1MinCalories { get; set; }
    public decimal Snack1MaxCalories { get; set; }
    public decimal Snack1MinProteinGrams { get; set; }
    public decimal Snack1MaxProteinGrams { get; set; }
    public decimal Snack1MinCarbsGrams { get; set; }
    public decimal Snack1MaxCarbsGrams { get; set; }
    public decimal Snack1MinFatGrams { get; set; }
    public decimal Snack1MaxFatGrams { get; set; }

    public decimal Snack2MinCalories { get; set; }
    public decimal Snack2MaxCalories { get; set; }
    public decimal Snack2MinProteinGrams { get; set; }
    public decimal Snack2MaxProteinGrams { get; set; }
    public decimal Snack2MinCarbsGrams { get; set; }
    public decimal Snack2MaxCarbsGrams { get; set; }
    public decimal Snack2MinFatGrams { get; set; }
    public decimal Snack2MaxFatGrams { get; set; }

}
public class Macros
{
    public decimal Calories { get; set; }
    public decimal Protein { get; set; }
    public decimal Carbs { get; set; }
    public decimal Fat { get; set; }
}