using System.Diagnostics;
using System.Timers;
using CommunityToolkit.Maui.Core;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Message;
using Newtonsoft.Json;
using RGPopup.Maui.Services;

namespace DrMaxMuscle.Views;

public partial class CompletePopup : Popup
{
    public event EventHandler OkButtonPress;
    private System.Timers.Timer aTimer;

    CancellationTokenSource _cts = new CancellationTokenSource();

    private string[] messageList =
        [
            "Nice work!",
            "Crushed it!",
            "Way to go!",
            "Nailed it!",
            "You rock!",
            "Fantastic!",
            "Boom!",
            "Woo-hoo!",
            "Congrats!",
            "Awesome!",
            "Unstoppable!",
            "Smashed it!",
            "Victory!",
            "Power up!",
            "Epic!",
            "Legend!",
            "Fire!",
            "Brilliant!",
            "Dynamite!",
            "Stellar!",
            "Way to go!",
            "Workout complete!",
            "Keep crushing it!",
            "You're a star!",
            "Victory!",
            "Epic work!",
            "Keep it up!",
            "You did it!",
            "That's the spirit!",
            "Looking strong!",
            "Great effort!",
            "Strength up!"
        ];


    //public CompletePopup()
    //{
    //    InitializeComponent();

    //    Debug.WriteLine("Complete Popup Showing");

    //    this.setNewMessage();

    //    aTimer = new System.Timers.Timer(2000);
    //    aTimer.Elapsed += OnTimedEvent;
    //    aTimer.Enabled = true;

    //    if (DeviceInfo.Current.Platform == DevicePlatform.Android)
    //        {
    //            DependencyService.Get<IAudio>().PlayAudioFile("complete_workout.mp3", true , true,true);
    //        }
    //        else if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
    //        {
    //            var message = new PlayAudioFileMessage(){
    //                IsFromComplete = true
    //            };
    //            MessagingCenter.Send(message, "PlayAudioFileMessage");
    //        }

    //}

    public CompletePopup()
    {
        InitializeComponent();
        Debug.WriteLine("Complete Popup Showing");

        var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
        mainGrid.WidthRequest = screenWidth;
        BackImage.HeightRequest = DeviceDisplay.MainDisplayInfo.Height;
        this.Closed += Popup_Closed;

        this.setNewMessage();

        RunAutoCloseAsync(_cts.Token); // Start async auto-close for this popup only

        if (DeviceInfo.Current.Platform == DevicePlatform.Android)
        {
            DependencyService.Get<IAudio>().PlayAudioFile("complete_workout.mp3", true, true, true);
        }
        else if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
        {
            var message = new PlayAudioFileMessage() { IsFromComplete = true };
            MessagingCenter.Send(message, "PlayAudioFileMessage");
        }
    }

    void setNewMessage()
    {
        var newMessage = this.GetNextTitle(this.messageList);
        MessageLabel.Text = newMessage;
    }

    void DrMuscleButtonShareTrial_Clicked(System.Object sender, System.EventArgs e)
    {
        Debug.WriteLine("Button share ttrial to dismiss popup.");

        this.Close();

        //if (PopupNavigation.Instance?.PopupStack != null && PopupNavigation.Instance.PopupStack.Count() > 0)
             //MauiProgram.SafeDismissTopPopup();
        //PopupNavigation.Instance.PopAsync();
        try
        {
            if (OkButtonPress != null)
            {
                OkButtonPress.Invoke(PopupAction.OK, EventArgs.Empty);
                OkButtonPress = null; // Prevent multiple invocations
            }
            aTimer?.Dispose();
        }
        catch (Exception ex)
        {

        }
    }

    private async void RunAutoCloseAsync(CancellationToken token)
    {
        try
        {
            await Task.Delay(2000, token);

            //MainThread.BeginInvokeOnMainThread(() =>
            //{
                if (token.IsCancellationRequested) return;

                Debug.WriteLine("Auto-closing popup.");
                this.Close(); // Will only close THIS popup instance

                if (OkButtonPress != null)
                {
                    OkButtonPress.Invoke(PopupAction.OK, EventArgs.Empty);
                    OkButtonPress = null;
                }
            //});
        }
        catch (TaskCanceledException)
        {
            Debug.WriteLine("Popup auto-close was cancelled."); 
        }
    }


    //private void OnTimedEvent(Object source, ElapsedEventArgs e)
    //{
    //    Debug.WriteLine("OnTimedEvent Calling.");

    //    if (aTimer != null)
    //        aTimer.Enabled = false;

    //    MainThread.BeginInvokeOnMainThread(() =>
    //    {
    //        try
    //        {
    //            Debug.WriteLine("OnTimedEvent Calling In try.");

    //            this.Close(); // UI thread only

    //            if (OkButtonPress != null)
    //            {
    //                Debug.WriteLine("OnTimedEvent OkButtonPress Calling In if.");
    //                OkButtonPress.Invoke(PopupAction.OK, EventArgs.Empty);
    //                OkButtonPress = null; // Prevent multiple invocations
    //            }

    //            Debug.WriteLine("OnTimedEvent OkButtonPress Calling after if.");

    //            if (aTimer != null)
    //                aTimer.Dispose();
    //        }
    //        catch (Exception ex)
    //        {
    //            Debug.WriteLine($"Exception : {ex}");
    //        }
    //        finally
    //        {
    //            // If needed, you can remove this if you're disposing the timer
    //            // aTimer.Enabled = true;
    //        }

    //        Debug.WriteLine("OnTimedEvent event finish...");
    //    });
    //}


    public string GetNextTitle(string[] titleList){
        string title = "";
        try
        {
            var data = Config.CheckmarkMessage;
            string[] usedTitle = JsonConvert.DeserializeObject<string[]>(data);
            title = titleList[new Random().Next(titleList.Length - 1)];
            if (usedTitle != null && usedTitle.Length > 0)
            {
                if (titleList.Length > usedTitle.Length)
                {
                    if (usedTitle.Contains(title))
                    {
                        GetNextTitle(titleList);
                    }
                    else
                    {
                        usedTitle.Append(title);
                        Config.CheckmarkMessage = JsonConvert.SerializeObject(usedTitle);
                    }
                }
                else
                {
                    usedTitle = new string[titleList.Length - 1];
                    usedTitle.Append(title);
                    Config.CheckmarkMessage = JsonConvert.SerializeObject(usedTitle);
                }
            }
            else
            {
                usedTitle = new string[titleList.Length - 1];
                usedTitle.Append(title);
                Config.CheckmarkMessage = JsonConvert.SerializeObject(usedTitle);
            }
        }
        catch (Exception ex)
        {
            return "";
        }
        return title;
    }


    private void Popup_Closed(object sender, PopupClosedEventArgs e)
    {
        _cts?.Cancel(); // Cancel timer when popup is dismissed
    }

}