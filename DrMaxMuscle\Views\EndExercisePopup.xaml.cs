using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using System;
using System.Collections.Generic;
using DrMuscleWebApiSharedModel;
using System.Linq;
using System.Threading.Tasks;
using System.Globalization;
using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Entity;
using DrMaxMuscle.Message;
using DrMaxMuscle.Screens;
using System.Threading;
using Microcharts;
using SkiaSharp;
using DrMaxMuscle.Utility;
using System.IO;
using Newtonsoft.Json;
using OxyPlot;
using DrMuscleWebApiSharedModel;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using System.Diagnostics;
using CommunityToolkit.Maui.Core;
using Microsoft.Maui.Devices;

namespace DrMaxMuscle.Views;

public partial class EndExercisePopup : Popup
{
    List<OneRMModel> _lastWorkoutLog = new List<OneRMModel>();
    private Dictionary<double, string> IndexToDateLabel = new Dictionary<double, string>();
    string strFacebook = "";
    bool isEstimated = false;
    long exerciseId = 0;
    private bool isPresented = false;
    private decimal _userBodyWeight = 0;
    private bool isHistoryLoaded = false;
    public ContentPage _navigation = null;
    public bool IsOpen = false;


    public EndExercisePopup(OneRMModel weight0, OneRMModel weight1, string exerciseName, long id)
    {
        Debug.WriteLine("EndExercisePopup calling");
        InitializeComponent();

        // Dynamically set the width of the Frame to 90% of the screen width
        var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
        MainFrame.WidthRequest = screenWidth * 0.9; // Assuming the Frame has x:Name="MainFrame"

        var screenheight = DeviceDisplay.MainDisplayInfo.Height / DeviceDisplay.MainDisplayInfo.Density;
        //MainFrame.HeightRequest = screenheight * 0.74;

        Device.BeginInvokeOnMainThread(() =>
        {
            lblExerciseName.Text = exerciseName;
            exerciseId = id;
            RefreshLocalized();
            //MyParticleCanvas.ParticleColors = AppThemeConstants.CalculateConfettieColors();
            NextExerciseButton.Clicked += NextExerciseButton_Clicked;
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
            });

            MessagingCenter.Subscribe<Message.ReceivedWatchMessage>(this, "ReceivedWatchMessage", (obj) =>
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    if (obj.PhoneToWatchModel.WatchMessageType == WatchMessageType.NextExercise)
                        NextExerciseButton_Clicked(NextExerciseButton, EventArgs.Empty);
                });

            });

            LoadBeforeServerCall(weight0, weight1, exerciseName);
        });
        
        Opened += (s, e) => IsOpen = true;
        Closed += (s, e) => IsOpen = false;
        this.Closed += Popup_Closed;
    }

    public async void LoadBeforeServerCall(OneRMModel weight1RM0, OneRMModel weight1RM1, string exerciseName)
    {
        try
        {

            DependencyService.Get<IFirebase>().SetScreenName("end_exercise_page");
            isEstimated = false;
            if (weight1RM1?.OneRM == null)
                isEstimated = true;
            // IconResultImage.Source = "up_arrow.png";
            lblResult1.IsVisible = true;
            lblResult21.IsVisible = true;
            lblResult3.IsVisible = true;
            lblResult4.IsVisible = true;

            lblResult3.Text = "";
            lblResult21.Text = "";
            lblResult4.Text = "";
            bool isTimeBased = false;// CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased;
            //plotView.Model = null;
            //Title = exerciseName;
            //MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.NextExercise, SetModel = new WorkoutLogSerieModelRef() }, "SendWatchMessage");


            NextExerciseButton.Text = AppResources.Continue;
            _lastWorkoutLog = new List<OneRMModel>();
            _lastWorkoutLog.Add(weight1RM0);
            _lastWorkoutLog.Add(weight1RM1);
            var beforeBefore1RM = LocalDBManager.Instance.GetDBExercise1RM(exerciseId);
            try
            {
                try
                {


                    if (CurrentLog.Instance.ExerciseLog.Exercice.IsWeighted)
                    {
                        if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                            _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value, CultureInfo.InvariantCulture);
                        foreach (var item in _lastWorkoutLog)
                        {
                            if (item.Weight != null)
                                item.OneRM = new MultiUnityWeight(ComputeOneRM(item.Weight.Kg + _userBodyWeight, item.Reps), "kg");

                        }

                    }
                }
                catch (Exception ex)
                {

                }


                OneRMModel beforeBeforeLast = null;
                OneRMModel beforeBeforeLast2 = null;
                if (beforeBefore1RM != null)
                {
                    isHistoryLoaded = true;
                    var oneRMModel = JsonConvert.DeserializeObject<List<OneRMModel>>(beforeBefore1RM.Last1RM);
                    if (oneRMModel.FirstOrDefault(x => x.IsAllowDelete) != null && oneRMModel.Count > 0)
                    {
                        bool isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";

                        beforeBeforeLast = new OneRMModel()
                        {
                            OneRM = new MultiUnityWeight(ComputeOneRM(
                    new MultiUnityWeight(
                            isKg ? oneRMModel[1].Weight.Kg : oneRMModel[1].Weight.Lb,
                            isKg ? "kg" : "lb").Kg + (CurrentLog.Instance.ExerciseLog.Exercice.IsWeighted ? _userBodyWeight : 0),
                    oneRMModel[1].Reps), WeightUnities.kg)
                        };

                        beforeBeforeLast2 = new OneRMModel()
                        {
                            OneRM = new MultiUnityWeight(ComputeOneRM(
                    new MultiUnityWeight(
                            isKg ? oneRMModel[0].Weight.Kg : oneRMModel[0].Weight.Lb,
                            isKg ? "kg" : "lb").Kg + (CurrentLog.Instance.ExerciseLog.Exercice.IsWeighted ? _userBodyWeight : 0),
                    oneRMModel[0].Reps), WeightUnities.kg)
                        };
                        oneRMModel[1].OneRM = beforeBeforeLast.OneRM;
                        _lastWorkoutLog.Add(oneRMModel[1]);
                        weight1RM1.LastLogDate = oneRMModel.First().LastLogDate;
                        weight1RM1.IsAllowDelete = oneRMModel.First().IsAllowDelete;
                        weight1RM1.OneRMDate = oneRMModel.First().OneRMDate;
                        if (weight1RM1.Reps == 0)
                        {
                            weight1RM1.Reps = oneRMModel.First().Reps;
                            weight1RM1.Weight = oneRMModel.First().Weight;
                            weight1RM1.OneRM = oneRMModel.First().OneRM;
                        }
                        weight1RM0.LastLogDate = DateTime.Now;
                        weight1RM0.OneRMDate = DateTime.Now;
                        weight1RM0.IsAllowDelete = true;
                    }
                }

                DateTime minDate = _lastWorkoutLog.Min(p => p.OneRMDate);
                DateTime maxDate = _lastWorkoutLog.Max(p => p.OneRMDate);
                OneRMModel last = _lastWorkoutLog.First(p => p.OneRMDate == maxDate);
                //OneRMModel beforeBeforeLast = _lastWorkoutLog.Where(p => p.OneRMDate > minDate && p.OneRMDate < maxDate).Skip(1).First();
                OneRMModel beforeLast = _lastWorkoutLog.Where(p => p.OneRMDate > minDate && p.OneRMDate < maxDate).FirstOrDefault();
                if (beforeLast == null)
                    beforeLast = _lastWorkoutLog.Where(p => p.OneRMDate == minDate).First();

                decimal weight0 = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                                                last.OneRM.Kg :
                                                last.OneRM.Lb;
                decimal weight1 = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                                                beforeLast.OneRM?.Kg ?? (decimal)0 :
                                                beforeLast.OneRM?.Lb ?? (decimal)0;
                if (weight1 == 0 && beforeBeforeLast2 != null)
                {
                    weight1 = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                                            beforeBeforeLast2.OneRM?.Kg ?? (decimal)0 :
                                            beforeBeforeLast2.OneRM?.Lb ?? (decimal)0;

                }

                decimal weight2 = 0;


                if (beforeBeforeLast != null)
                    weight2 = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                                                    beforeBeforeLast.OneRM?.Kg ?? (decimal)0 :
                                                    beforeBeforeLast.OneRM?.Lb ?? (decimal)0;
                else
                    weight2 = 0;
                decimal reps0 = last.Reps;
                decimal reps1 = beforeLast.Reps;
                decimal progressNumb = 0;
                try
                {
                    lblResult1.Text = "Strength up";//string.Format("{0} {1}!", AppResources.Congratulations, LocalDBManager.Instance.GetDBSetting("firstname").Value);
                    ImgName.Source = "trophy.png";


                    if (weight0 > weight1 && weight0 > weight2)
                    {
                        lblResult1.Text = "Strength up";
                        ImgName.Source = "trophy.png";
                    }
                    else
                    {
                        ImgName.Source = "truestate.png";
                        lblResult1.Text = "Lift successful";
                    }

                    if (isTimeBased)
                    {
                        lblResult21.Text = string.Format("{0} {1}", _lastWorkoutLog.ElementAt(0).Reps, "Secs");
                    }
                    else
                        lblResult21.Text = string.Format("{0:0.#} {1}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                        Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1) :
                        Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Lb, 1),
                        LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs").ReplaceWithDot();



                    try
                    {

                        if (LocalDBManager.Instance.GetDBSetting($"WorkoutStrenth{DateTime.Now.Year}") == null)
                            LocalDBManager.Instance.SetDBSetting($"WorkoutStrenth{DateTime.Now.Year}", Convert.ToString(Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1)).ReplaceWithDot());
                        else
                        {
                            var strenthCount = Convert.ToDouble(LocalDBManager.Instance.GetDBSetting($"WorkoutStrenth{DateTime.Now.Year}")?.Value?.ReplaceWithDot(), System.Globalization.CultureInfo.InvariantCulture);
                            strenthCount += (double)Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1);
                            LocalDBManager.Instance.SetDBSetting($"WorkoutStrenth{DateTime.Now.Year}", $"{strenthCount}");
                        }

                    }
                    catch (Exception ex)
                    {

                    }

                    try
                    {

                        if (LocalDBManager.Instance.GetDBSetting($"ExerciseStrenth{DateTime.Now.Year}") == null)
                        {
                            LocalDBManager.Instance.SetDBSetting($"ExerciseStrenth{DateTime.Now.Year}", Convert.ToString(Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1))?.ReplaceWithDot());
                            LocalDBManager.Instance.SetDBSetting($"ExerciseStrenthName{DateTime.Now.Year}", exerciseName);
                        }
                        else
                        {
                            var strenthCount = LocalDBManager.Instance.GetDBSetting($"ExerciseStrenth{DateTime.Now.Year}").Value.ReplaceWithDot();
                            //strenthCount += (double)Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1);
                            LocalDBManager.Instance.SetDBSetting($"ExerciseStrenth{DateTime.Now.Year}", $"{strenthCount}|{Convert.ToString(Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1)).ReplaceWithDot()}");

                            var exerciseNames = LocalDBManager.Instance.GetDBSetting($"ExerciseStrenthName{DateTime.Now.Year}").Value;
                            //strenthCount += (double)Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1);
                            LocalDBManager.Instance.SetDBSetting($"ExerciseStrenthName{DateTime.Now.Year}", $"{exerciseNames}|{exerciseName}");
                        }

                    }
                    catch (Exception ex)
                    {

                    }

                    lblResult3.Text = string.Format("{0:0.#} {1}", LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                        Math.Round(_lastWorkoutLog.ElementAt(1).OneRM?.Kg ?? 0, 1) :
                        Math.Round(_lastWorkoutLog.ElementAt(1).OneRM?.Lb ?? 0, 1),
                        LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs").ReplaceWithDot();
                    lblResult3.IsVisible = true;


                    lblResult33.Text = "Last workout";
                    //lblResult44.Text = "Progress";

                    if (!isEstimated)
                    {
                        progressNumb = Math.Round(((Math.Round(weight0, 1) - Math.Round(weight1, 1)) * 100) / Math.Round(weight1, 1), 1);
                        lblResult4.Text = string.Format("{0}{1:0.#}%{2}", progressNumb > 0 ? "+" : "", progressNumb, progressNumb > 0 ? "!" : "").ReplaceWithDot();
                        if (isTimeBased)
                        {
                            progressNumb = Math.Round(((reps0 - reps1) * 100) / reps1, 1);
                            lblResult4.Text = string.Format("{0}{1:0.#}%", progressNumb > 0 ? "+" : "", progressNumb).ReplaceWithDot();
                        }

                    }
                    else
                    {
                        progressNumb = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                        Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1) :
                        Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Lb, 1);

                        lblResult4.Text = string.Format("{0}{1:0.#} {2}{3}", progressNumb > 0 ? "+" : "", progressNumb,
                        LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", progressNumb > 0 ? "!" : "").ReplaceWithDot();
                        lblResult3.Text = "0";
                        if (isTimeBased)
                        {
                            progressNumb = _lastWorkoutLog.ElementAt(0).Reps;
                            lblResult4.Text = string.Format("{0:0}%", progressNumb).ReplaceWithDot();
                        }

                    }
                }
                catch (Exception ex)
                {

                }
                strFacebook = "";
                //If deload...
                if (Math.Round(weight0, 1) == Math.Round(weight1, 1))
                {
                    ImgName.Source = "truestate.png";
                    lblResult1.Text = "Lift successful";// string.Format("{0} {1}", AppResources.WellDone, LocalDBManager.Instance.GetDBSetting("firstname").Value);

                }
                else if (weight0 < (weight1 * (decimal)0.98) && weight0 < (weight1 - 2))
                {

                    lblResult1.IsVisible = true;

                    if (isEstimated)
                    {
                        lblResult1.Text = "Strength up";
                        ImgName.Source = "trophy.png";
                        // IconResultImage.Source = "up_arrow.png";
                        //Set button text here:

                        lblResult1.IsVisible = true;

                    }
                    else if (CurrentLog.Instance.RecommendationsByExercise.ContainsKey(exerciseId) && CurrentLog.Instance.RecommendationsByExercise[exerciseId].IsLightSession)
                    {
                        ImgName.Source = "truestate.png";
                        lblResult1.Text = "Strength down"; //"Light session successful";// string.Format("{0} {1}!", AppResources.WellDone, LocalDBManager.Instance.GetDBSetting("firstname").Value);

                        // IconResultImage.Source = "down_arrow.png";
                    }
                    else if (LocalDBManager.Instance.GetDBSetting("RecoDeload")?.Value == "false")
                    {
                        lblResult1.Text = "Strength down";//string.Format("{0} {1}!", AppResources.Attention ,LocalDBManager.Instance.GetDBSetting("firstname").Value);
                        ImgName.Source = "alert_ic_blue.png";
                        //  IconResultImage.Source = "down_arrow.png";
                    }
                    //If 2e workout au retour de deload...
                    else
                    {
                        ImgName.Source = "truestate.png";
                        lblResult1.Text = "Strength down"; //"Deload successful";//string.Format(AppResources.DeloadSuccessful);
                    }

                }
                //else if égal

                //else if légère diminution
                else if (weight0 >= (weight1 * (decimal)0.98) && weight0 <= weight1 || weight0 < (weight1 * (decimal)0.98) && weight0 >= (weight1 - 2))
                {
                    ImgName.Source = "alert_ic_blue.png";
                    lblResult1.Text = "Strength down";

                }
                //Sinon (if pas deload...)
                else
                {
                    //IconResultImage.Source = "up_arrow.png";
                    ImgName.Source = "trophy.png";
                    //Set button text here:

                    lblResult1.IsVisible = true;

                }



                if (isEstimated)
                {
                    lblResult1.Text = "Strength up";
                    ImgName.Source = "trophy.png";
                    //IconResultImage.Source = "up_arrow.png";
                    //Set button text here:
                    lblResult1.IsVisible = true;

                    try
                    {

                        if (LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout") == null)
                            LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", "1");
                        else
                        {
                            var recordCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout")?.Value ?? "0");
                            recordCount += 1;
                            LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", $"{recordCount}");
                        }


                    }
                    catch (Exception ex)
                    {

                    }
                }
                if (lblResult1.Text == "Strength up")
                {
                    NewRecordModel newModel = new NewRecordModel();
                    if (((App)Application.Current).NewRecordModelContext.NewRecordList.Where(x => x.ExerciseName == exerciseName).FirstOrDefault() != null)
                    {
                        newModel = ((App)Application.Current).NewRecordModelContext.NewRecordList.Where(x => x.ExerciseName == exerciseName).FirstOrDefault();
                        if (newModel == null)
                            newModel = new NewRecordModel();
                    }
                    newModel.ExerciseName = exerciseName;
                    if (!isEstimated)
                        newModel.Prev1RM = beforeLast.OneRM;
                    newModel.New1RM = last.OneRM;
                    newModel.IsMobilityOrCardio = CurrentLog.Instance.ExerciseLog.Exercice.IsFlexibility || CurrentLog.Instance.ExerciseLog.Exercice.BodyPartId == 12;
                    newModel.ExercisePercentage = lblResult4.Text.Replace("+", "").Replace("!", "");
                    newModel.ExercisePercentageNumber = progressNumb;
                    if (((App)Application.Current).NewRecordModelContext.NewRecordList.Where(x => x.ExerciseName == exerciseName).FirstOrDefault() == null)
                    {
                        ((App)Application.Current).NewRecordModelContext.NewRecordList.Add(newModel);
                        ((App)Application.Current).NewRecordModelContext.SaveContexts();
                    }
                }
                if (lblResult1.Text == "Strength up" && !isEstimated)
                {
                    try
                    {

                        if (LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout") == null)
                            LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", "1");
                        else
                        {
                            var recordCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout")?.Value ??"0");
                            recordCount += 1;
                            LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", $"{recordCount}");
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                }
                try
                {
                    if (beforeBefore1RM != null && weight1 != 0)
                    {

                        
                            try
                            {


                                if (CurrentLog.Instance.ExerciseLog?.Exercice?.IsWeighted == true)
                                {

                                    if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                                        _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value, CultureInfo.InvariantCulture);
                                    foreach (var item in _lastWorkoutLog)
                                    {
                                        item.OneRM = new MultiUnityWeight(ComputeOneRM(item.Weight.Kg + _userBodyWeight, item.Reps), "kg");
                                    }
                                }

                                var chartSerie = new ChartSerie() { Name = AppResources.MAXSTRENGTHESTIMATELAST3WORKOUTS.ToLower().FirstCharToUpper(), Color = SKColor.Parse("#38418C") };
                                List<ChartSerie> chartSeries = new List<ChartSerie>();

                                List<ChartEntry> entries = new List<ChartEntry>();



                                int i = 1;
                                foreach (OneRMModel m in _lastWorkoutLog.OrderBy(w => w.OneRMDate))
                                {
                                    if (i == 2 && !m.IsAllowDelete)
                                        isEstimated = true;
                                    switch (LocalDBManager.Instance.GetDBSetting("massunit")?.Value)
                                    {
                                        default:
                                        case "kg":

                                            if (!m.IsAllowDelete)
                                                entries.Add(new ChartEntry(0) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = "0" });
                                            else
                                            {
                                                var val = (float)Math.Round(m.OneRM?.Kg ?? 0);
                                                entries.Add(new ChartEntry(val) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = val.ToString() });

                                            }
                                            break;
                                        case "lb":
                                            if (!m.IsAllowDelete)
                                                entries.Add(new ChartEntry(0) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = "0" });
                                            else
                                            {
                                                var val = (float)Math.Round(m.OneRM?.Lb ?? 0);
                                                entries.Add(new ChartEntry(val) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = val.ToString() });
                                            }
                                            break;
                                    }
                                    i++;
                                }


                                chartSerie.Entries = entries;
                                chartSeries.Add(chartSerie);
                            MainThread.BeginInvokeOnMainThread(() =>
                            {
                                chartView.Chart = new LineChart
                                {
                                    LabelOrientation = Orientation.Vertical,
                                    ValueLabelOrientation = Orientation.Vertical,
                                    LabelTextSize = Device.RuntimePlatform == Device.iOS ? 22 : 26,
                                    ValueLabelTextSize = Device.RuntimePlatform == Device.iOS ? 22 : 26,
                                    SerieLabelTextSize = Device.RuntimePlatform == Device.iOS ? 20 : 24,
                                    BackgroundColor = SKColors.Transparent,
                                    EnableYFadeOutGradient = false,
                                    PointMode = PointMode.Circle,
                                    LegendOption = SeriesLegendOption.None,
                                    LineMode = LineMode.Spline,
                                    IsAnimated = false,
                                    Series = chartSeries,
                                };
                            });
                                
                            }
                            catch (Exception ex)
                            {
                                beforeBefore1RM = null;
                                isHistoryLoaded = false;
                            }
                            //Congratulations message

                            //DateTime minDate = _lastWorkoutLog.Min(p => p.OneRMDate);
                            //DateTime maxDate = _lastWorkoutLog.Max(p => p.OneRMDate);
                            //OneRMModel last = _lastWorkoutLog.First(p => p.OneRMDate == maxDate);
                            //OneRMModel beforeLast = _lastWorkoutLog.Where(p => p.OneRMDate > minDate && p.OneRMDate < maxDate).First();
                            //OneRMModel beforeBeforeLast = _lastWorkoutLog.Where(p => p.OneRMDate == minDate).First();

                            //decimal weight0 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                            //                                last.OneRM.Kg :
                            //                                last.OneRM.Lb;
                            //decimal weight1 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                            //                                beforeLast.OneRM.Kg :
                            //                                beforeLast.OneRM.Lb;
                            //decimal weight2 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                            //                                beforeBeforeLast.OneRM.Kg :
                            //                                beforeBeforeLast.OneRM.Lb;

                            //decimal reps0 = last.Reps;
                            //decimal reps1 = beforeLast.Reps;
                            //decimal progressNumb = 0;

                            try
                            {

                                if (LocalDBManager.Instance.GetDBSetting($"WorkoutStrenth{DateTime.Now.Year}") == null)
                                    LocalDBManager.Instance.SetDBSetting($"WorkoutStrenth{DateTime.Now.Year}", Convert.ToString(Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1)).ReplaceWithDot());
                                else
                                {
                                    var strenthCount = Convert.ToDouble(LocalDBManager.Instance.GetDBSetting($"WorkoutStrenth{DateTime.Now.Year}")?.Value?.ReplaceWithDot(), System.Globalization.CultureInfo.InvariantCulture);
                                    strenthCount += (double)Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1);
                                    LocalDBManager.Instance.SetDBSetting($"WorkoutStrenth{DateTime.Now.Year}", $"{strenthCount}");
                                }

                            }
                            catch (Exception ex)
                            {

                            }


                            lblResult33.Text = "Last workout";




                            strFacebook = "";
                            //If deload...

                            if (weight0 < (weight1 * (decimal)0.98) && weight0 < (weight1 - 2))
                            {


                                if (isEstimated)
                                {


                                    //Set button text here:

                                    try
                                    {

                                        if (LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout") == null)
                                            LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", "1");
                                        else
                                        {
                                            var recordCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout")?.Value??"0");
                                            recordCount += 1;
                                            LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", $"{recordCount}");
                                        }


                                    }
                                    catch (Exception ex)
                                    {

                                    }
                                    strFacebook = string.Format("{0} {1} {2} {3:f} {4}{5}", "I just smashed a new record!", CurrentLog.Instance.ExerciseLog.Exercice.Label, "is now", LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                                _lastWorkoutLog.ElementAt(0).OneRM.Kg :
                                _lastWorkoutLog.ElementAt(0).OneRM.Lb, LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? "kg" : "lbs", ". I train using Dr. Muscle. Get your invitation at:");

                                }

                            }
                            //else if égal
                            else if (weight0 == weight1)
                            {


                            }
                            //else if légère diminution
                            else if (weight0 >= (weight1 * (decimal)0.98) && weight0 <= weight1 || weight0 < (weight1 * (decimal)0.98) && weight0 >= (weight1 - 2))
                            {


                            }



                      


                    }
                }
                catch (Exception ex)
                {

                }
            }
            catch (Exception e)
            {

            }
            if (lblResult1.Text == "Strength up")
            {
                lblResult4.TextColor = AppThemeConstants.BlueColor;
                //TODO: MAUI
                //MyParticleCanvas.IsActive = true;
                //MyParticleCanvas.IsRunning = true;

            }

        }
        catch (Exception ex)
        {

        }
    }


    protected async Task<bool> ConnectionErrorConfirmPopup()
    {
        try{
        if (isPresented)
            return true;
        isPresented = true;

        var results = await HelperClass.DisplayCustomPopupForResult("Loading error","Slow or no connection. Please check and try again.","Retry loading","Cancel");


        // ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
        // {
        //     Title = "Loading error",
        //     Message = "Slow or no connection. Please check and try again.",
        //     AndroidStyleId = DependencyService.Get<IStyles>()
        //         .GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OkText = "Retry loading",
        //     CancelText = "Cancel",

        // };
        // var results = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
        isPresented = false;
        if (results == PopupAction.OK)
            {
                return true;
            }
        }
        catch (Exception ex)
        {

        }

        return false;
    }
    public async void OnBeforeShow()
    {

        try
        {


            isEstimated = false;


            bool isTimeBased = false;// CurrentLog.Instance.ExerciseLog.Exercice.IsTimeBased;
                                     //plotView.Model = null;
                                     //Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;
                                     // MessagingCenter.Send<SendWatchMessage>(new SendWatchMessage() { WatchMessageType = WatchMessageType.NextExercise, SetModel = new WorkoutLogSerieModelRef() }, "SendWatchMessage");


            NextExerciseButton.Text = AppResources.Continue;


            try
            {
                var key = exerciseId;
                if (CurrentLog.Instance.Exercise1RM != null && CurrentLog.Instance.Exercise1RM.ContainsKey(key) && CurrentLog.Instance.LastSerieModelList != null && CurrentLog.Instance.LastSerieModelList.Count > 0)
                {
                    _lastWorkoutLog = CurrentLog.Instance.Exercise1RM?[key];
                    if (_lastWorkoutLog != null)
                    {
                        List<decimal> listOf1Rm = new List<decimal>();
                        foreach (var item in CurrentLog.Instance.LastSerieModelList)
                        {
                            listOf1Rm.Add(ComputeOneRM(item.Weight.Kg, item.Reps));
                        }
                        _lastWorkoutLog.Remove(_lastWorkoutLog.Last());
                        _lastWorkoutLog.Insert(0, new OneRMModel()
                        {
                            ExerciseId = key,
                            OneRMDate = DateTime.Now,
                            OneRM = new MultiUnityWeight(listOf1Rm.Max(), "kg"),
                            LastLogDate = DateTime.Now
                        });

                    }
                }
                else
                {

                    _lastWorkoutLog = await DrMuscleRestClient.Instance.GetOneRMForExerciseWithoutLoader(
                        new GetOneRMforExerciseModel()
                        {
                            Username = LocalDBManager.Instance.GetDBSetting("email")?.Value,
                            Massunit = LocalDBManager.Instance.GetDBSetting("massunit")?.Value,
                            ExerciseId = key
                        }
                    );
                    try
                    {
                        LocalDBManager.Instance.SetDBExerciceIRM(key, JsonConvert.SerializeObject(_lastWorkoutLog));
                    }
                    catch (Exception ex)
                    {

                    }

                }

                if (_lastWorkoutLog == null)
                {
                    if (await ConnectionErrorConfirmPopup())
                        OnBeforeShow();
                    return;
                }
                if (!isHistoryLoaded)
                {
                    
                        if (CurrentLog.Instance.ExerciseLog?.Exercice?.IsWeighted == true)
                        {

                            if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
                                _userBodyWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value, CultureInfo.InvariantCulture);
                            foreach (var item in _lastWorkoutLog)
                            {
                                item.OneRM = new MultiUnityWeight(ComputeOneRM(item.Weight.Kg + _userBodyWeight, item.Reps), "kg");
                            }
                        }

                        var chartSerie = new ChartSerie() { Name = AppResources.MAXSTRENGTHESTIMATELAST3WORKOUTS.ToLower().FirstCharToUpper(), Color = SKColor.Parse("#38418C") };
                        List<ChartSerie> chartSeries = new List<ChartSerie>();

                        List<ChartEntry> entries = new List<ChartEntry>();



                        int i = 1;
                        foreach (OneRMModel m in _lastWorkoutLog.OrderBy(w => w.OneRMDate))
                        {
                            if (i == 2 && !m.IsAllowDelete)
                                isEstimated = true;
                            //if (!m.IsAllowDelete)
                            //    yAxis.Minimum = -120;
                            switch (LocalDBManager.Instance.GetDBSetting("massunit")?.Value)
                            {
                                default:
                                case "kg":

                                    if (!m.IsAllowDelete)
                                        entries.Add(new ChartEntry(0) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = "0" });
                                    else
                                    {
                                        var val = (float)Math.Round(m.OneRM.Kg);
                                        entries.Add(new ChartEntry(val) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = val.ToString() });
                                        //var val = (float)Math.Round(inKg ? data.Average.Kg : data.Average.Lb);
                                        //s1.Points.Add(new DataPoint(i, Convert.ToDouble(m.OneRM.Kg)));
                                    }
                                    break;
                                case "lb":
                                    if (!m.IsAllowDelete)
                                        entries.Add(new ChartEntry(0) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = "0" });
                                    else
                                    {
                                        var val = (float)Math.Round(m.OneRM.Lb);
                                        entries.Add(new ChartEntry(val) { Label = m.OneRMDate.ToLocalTime().ToString("MMM dd"), ValueLabel = val.ToString() });
                                    }
                                    break;
                            }
                            //IndexToDateLabel.Add(i, m.OneRMDate.ToLocalTime().ToString("MMM dd"));

                            i++;
                        }

                        // plotModel.Series.Add(s1);
                        //plotView.Model = plotModel;





                        chartSerie.Entries = entries;
                        chartSeries.Add(chartSerie);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        chartView.Chart = new LineChart
                        {
                            LabelOrientation = Orientation.Vertical,
                            ValueLabelOrientation = Orientation.Vertical,
                            LabelTextSize = Device.RuntimePlatform == Device.iOS ? 22 : 26,
                            ValueLabelTextSize = Device.RuntimePlatform == Device.iOS ? 22 : 26,
                            SerieLabelTextSize = Device.RuntimePlatform == Device.iOS ? 20 : 24,
                            LegendOption = SeriesLegendOption.None,
                            BackgroundColor = SKColors.Transparent,
                            IsAnimated = false,
                            Series = chartSeries,
                        };
                    });

                        //Congratulations message

                        DateTime minDate = _lastWorkoutLog.Min(p => p.OneRMDate);
                        DateTime maxDate = _lastWorkoutLog.Max(p => p.OneRMDate);
                        OneRMModel last = _lastWorkoutLog.First(p => p.OneRMDate == maxDate);
                        OneRMModel beforeLast = _lastWorkoutLog.Where(p => p.OneRMDate > minDate && p.OneRMDate < maxDate).First();
                        OneRMModel beforeBeforeLast = _lastWorkoutLog.Where(p => p.OneRMDate == minDate).First();

                        decimal weight0 = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                                                        last.OneRM.Kg :
                                                        last.OneRM.Lb;
                        decimal weight1 = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                                                        beforeLast.OneRM.Kg :
                                                        beforeLast.OneRM.Lb;
                        decimal weight2 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                        beforeBeforeLast.OneRM.Kg :
                                                        beforeBeforeLast.OneRM.Lb;

                        decimal reps0 = last.Reps;
                        decimal reps1 = beforeLast.Reps;
                        decimal progressNumb = 0;
                        try
                        {
                            try
                            {

                                if (LocalDBManager.Instance.GetDBSetting($"WorkoutStrenth{DateTime.Now.Year}") == null)
                                    LocalDBManager.Instance.SetDBSetting($"WorkoutStrenth{DateTime.Now.Year}", Convert.ToString(Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1)).ReplaceWithDot());
                                else
                                {
                                    var strenthCount = Convert.ToDouble(LocalDBManager.Instance.GetDBSetting($"WorkoutStrenth{DateTime.Now.Year}")?.Value?.ReplaceWithDot(), System.Globalization.CultureInfo.InvariantCulture);
                                    strenthCount += (double)Math.Round(_lastWorkoutLog.ElementAt(0).OneRM.Kg, 1);
                                    LocalDBManager.Instance.SetDBSetting($"WorkoutStrenth{DateTime.Now.Year}", $"{strenthCount}");
                                }

                            }
                            catch (Exception ex)
                            {

                            }


                            lblResult33.Text = "Last workout";



                        }
                        catch (Exception ex)
                        {

                        }
                        strFacebook = "";
                        //If deload...

                        if (weight0 < (weight1 * (decimal)0.98) && weight0 < (weight1 - 2))
                        {


                            if (isEstimated)
                            {


                                //Set button text here:

                                try
                                {

                                    if (LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout") == null)
                                        LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", "1");
                                    else
                                    {
                                        var recordCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout")?.Value ?? "0");
                                        recordCount += 1;
                                        LocalDBManager.Instance.SetDBSetting($"RecordFinishWorkout", $"{recordCount}");
                                    }

                                    //NewRecordModel newModel = new NewRecordModel();
                                    //newModel.ExerciseName = CurrentLog.Instance.ExerciseLog.Exercice.Label;
                                    //newModel.New1RM = last.OneRM;
                                    //newModel.ExercisePercentageNumber = progressNumb;
                                    //((App)Application.Current).NewRecordModelContext.NewRecordList.Add(newModel);
                                    //((App)Application.Current).NewRecordModelContext.SaveContexts();
                                }
                                catch (Exception ex)
                                {

                                }
                                strFacebook = string.Format("{0} {1} {2} {3:f} {4}{5}", "I just smashed a new record!", CurrentLog.Instance.ExerciseLog.Exercice.Label, "is now", LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                            _lastWorkoutLog.ElementAt(0).OneRM.Kg :
                            _lastWorkoutLog.ElementAt(0).OneRM.Lb, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", ". I train using Dr. Muscle. Get your invitation at:");

                            }

                        }
                        //else if égal
                        else if (weight0 == weight1)
                        {


                        }
                        //else if légère diminution
                        else if (weight0 >= (weight1 * (decimal)0.98) && weight0 <= weight1 || weight0 < (weight1 * (decimal)0.98) && weight0 >= (weight1 - 2))
                        {


                        }
                        //Sinon (if pas deload...)
                        else
                        {


                            strFacebook = string.Format("{0} {1} {2} {3:f} {4}{5}", "I just smashed a new record!", CurrentLog.Instance.ExerciseLog.Exercice.Label, "is now", LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ?
                            _lastWorkoutLog.ElementAt(0).OneRM.Kg :
                            _lastWorkoutLog.ElementAt(0).OneRM.Lb, LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? "kg" : "lbs", ". I train using Dr. Muscle. Get your invitation at:");

                        }

    
                }

            }
            catch (Exception e)
            {
                //await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Ok","");
                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseCheckInternetConnection,
                //     Title = AppResources.ConnectionError
                // });
            }

            if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("KenkoSingleExercisePage"))
            {
                MessagingCenter.Send<UpdatedWorkoutMessage>(new UpdatedWorkoutMessage() { OnlyRefresh = true }, "UpdatedWorkoutMessage");
            }
            else
            {
                var dt = DateTime.Now.AddMinutes(30);
                var timeSpan = new TimeSpan(0, dt.Hour, dt.Minute, 0);// DateTime.Now.AddMinutes(2) - DateTime.Now;////
                DependencyService.Get<IAlarmAndNotificationService>().ScheduleNotification("Dr. Muscle", "You forgot to save your workout!", timeSpan, 1352, NotificationInterval.Week, Convert.ToString(exerciseId));
            }

            if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("AllExercisePage") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("AllExercisesView") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("KenkoSingleExercisePage"))
            {

            }
            else
            {
                CurrentLog.Instance.IsFromEndExercise = false;

                CurrentLog.Instance.IsFromEndExercise = true;

                if (Device.RuntimePlatform.Equals(Device.Android))
                    await Task.Delay(100);
                //MessagingCenter.Send<LoadNextExercise>(new LoadNextExercise(), "LoadNextExercise");
                if (CurrentLog.Instance.IsFinishedWorkoutWithExercise && Device.RuntimePlatform.Equals(Device.iOS))
                {
                    //CurrentLog.Instance.IsFinishedWorkoutWithExercise = false;
                    //MessagingCenter.Send<EndExercisePopup>(this, "EndExercisePopup");
                    return;
                }
                //if (Device.RuntimePlatform.Equals(Device.iOS))
                //    MessagingCenter.Send<LoadNextExercise>(new LoadNextExercise(), "LoadNextExercise");

            }
        }
        catch (Exception e)
        {

        }
    }

    public decimal ComputeOneRM(decimal weight, int reps)
    {
        // Mayhew
        //return (100 * weight) / (decimal)(52.2 + 41.9 * Math.Exp(-0.055 * reps));
        // Epey
        return (decimal)(Constants.AppThemeConstants.Coeficent() * reps) * weight + weight;
    }

    private void RefreshLocalized()
    {
        NextExerciseButton.Text = Resx.AppResources.NextExercise;
    }

    //private static bool _isRunning = false;


    private async void NextExerciseButton_Clicked(object sender, EventArgs e)
    {
        //if (_isRunning)
        //    return;

        //_isRunning = true;

        //try
        //{
        //    // Disable buttons immediately to prevent second tap
        //    if (sender is VisualElement ve)
        //        ve.IsEnabled = false;

        //}
        //catch (Exception ex)
        //{
        //    Debug.WriteLine($"Error closing popup: {ex.Message}");
        //}

        try
        {
            if (!CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoWorkoutPage"))
            {
                if (((App)Application.Current).UserWorkoutContexts.workouts != null)
                {
                    ((App)Application.Current).UserWorkoutContexts.workouts.LastWorkoutDate = DateTime.UtcNow;
                    ((App)Application.Current).UserWorkoutContexts.SaveContexts();
                }
            }
            try
            {
                if(App.isUnfinishExercises == true && App.isUnfinishSets == true)
                {
                    await Task.Delay(1000);
                    App.isUnfinishExercises = false;
                    App.isUnfinishSets = false;
                }
            }
            catch (Exception ex)
            {

            }
            try
            {
                var key = exerciseId;
                if (CurrentLog.Instance.RecommendationsByExercise.ContainsKey(key))
                {
                    var setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle")?.Value;
                    if (LocalDBManager.Instance.GetDBReco("RReps" + key + setStyle + "challenge")?.Value == "max" && !Config.FirstChallenge && CurrentLog.Instance.IsAskedChallenge)
                    {
                        Config.FirstChallenge = true;
                        var waitHandle = new EventWaitHandle(false
                            , EventResetMode.AutoReset);
                        var modalPage = new Views.RGGeneralPopup("emptystar.png", "Congratulations!", "You completed your first challenge", "Continue", new Thickness(0, 0, 0, 0));
                        modalPage.Closed += (sender2, e2) =>
                        {
                            waitHandle.Set();
                        };
                        //this.Opacity = 0.0;
                        this.Color = Colors.Transparent;
                        await Application.Current.MainPage.ShowPopupAsync(modalPage);

                        await Task.Run(() => waitHandle.WaitOne());
                    }
                    else if (CurrentLog.Instance.RecommendationsByExercise[key].IsDeload && !Config.FirstDeload)
                    {
                        Config.FirstDeload = true;
                        var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                        var modalPage = new Views.RGGeneralPopup("emptystar.png", "Congratulations!", "First deload done", "Continue", new Thickness(0, 0, 0, 0));
                        modalPage.Closed += (sender2, e2) =>
                        {
                            waitHandle.Set();
                        };
                        //this.Opacity = 0.0;
                        await Application.Current.MainPage.ShowPopupAsync(modalPage);
                        await Task.Run(() => waitHandle.WaitOne());
                    }
                    else if (CurrentLog.Instance.RecommendationsByExercise[key].IsLightSession && !Config.FirstLightSession)
                    {
                        Config.FirstLightSession = true;
                        var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                        var modalPage = new Views.RGGeneralPopup("emptystar.png", "Congratulations!", "First light session done", "Continue", new Thickness(0, 0, 0, 0));
                        modalPage.Closed += (sender2, e2) =>
                        {
                            waitHandle.Set();
                        };
                        //this.Opacity = 0.0;
                        await Application.Current.MainPage.ShowPopupAsync(modalPage);

                        await Task.Run(() => waitHandle.WaitOne());
                    }
                }
            }
            catch (Exception ex)
            {

            }
            if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("AllExercisePage") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("AllExercisesView") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("KenkoSingleExercisePage"))
            {
                //TODO: MAUI
                //if (Device.RuntimePlatform == Device.Android)
                //{
                //    await PagesFactory.PushAsyncWithoutBefore<Screens.Exercises.AllExercisePage>();
                //}
                //else
                // Optional: Add slight delay to let iOS chill before removing view
                await Task.Delay(50);
                if (_navigation?.Navigation?.NavigationStack?.Count > 1)
                {
                    try
                    {
                        await _navigation?.Navigation?.PopAsync();

                        await this.CloseAsync();
                        //await MauiProgram.SafeDismissAllPopups();
                    }
                    catch (NotSupportedException ex)
                    {
                    }
                }
                
                //if (PopupNavigation.Instance.PopupStack?.Count > 0)
                //    await PopupNavigation.Instance.PopAllAsync();
            }
            else
            {
                try
                {
                    // Optional: Add slight delay to let iOS chill before removing view
                    //await Task.Delay(50);
                    await this.CloseAsync();

                    //await MauiProgram.SafeDismissAllPopups();
                    //if (PopupNavigation.Instance.PopupStack?.Count > 0)
                    //{
                    //    await PopupNavigation.Instance.PopAllAsync();
                    //}
                    if (CurrentLog.Instance.IsFinishedWorkoutWithExercise && Device.RuntimePlatform.Equals(Device.iOS))
                    {
                        CurrentLog.Instance.IsFinishedWorkoutWithExercise = false;
                        MessagingCenter.Send<LoadNextExercise>(new LoadNextExercise(), "LoadNextExercise");
                        return;
                    }
                    else if(Device.RuntimePlatform.Equals(Device.iOS))
                    {
                        MessagingCenter.Send<LoadNextExercise>(new LoadNextExercise(), "LoadNextExercise");
                    }

                    // Send the message to load the next exercise
                    if (Device.RuntimePlatform.Equals(Device.Android))
                        MessagingCenter.Send<LoadNextExercise>(new LoadNextExercise(), "LoadNextExercise");
                    else if(Device.RuntimePlatform.Equals(Device.iOS))
                        MessagingCenter.Send<LoadNextExercise>(new LoadNextExercise(), "LoadNextExercise");
                }
                catch (NotSupportedException ex)
                {
                }
            }
        }
        catch (Exception ex)
        {

        }
        finally
        {
            //_isRunning = false;
        }
    }



    private string _formatter(double d)
    {
        try
        {
            return IndexToDateLabel.ContainsKey(d) ? IndexToDateLabel[d] : "";
        }
        catch (Exception ex)
        {
            return "";
        }
    }

    private void Popup_Closed(object sender, PopupClosedEventArgs e)
    {
        Debug.WriteLine("Popup Closed");
        //base.OnDisappearing();
        try
        {
            //TODO: MAUI
            //MyParticleCanvas.IsActive = false;
            //MyParticleCanvas.IsRunning = false;
            //MyParticleCanvas = null;

        }
        catch (Exception ex)
        {

        }
    }

    async void ShareWithFBButton_Clicked(object sender, EventArgs e)
    {
        
    }



    void TapGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
    {
        //Device.OpenUri(new Uri());
        Browser.OpenAsync("http://dr-muscle.com/deload/");
    }

    async void ShareExerciseButton_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {

        //get the image
        var ImageStream = await ScrollContentToShare.CaptureAsync();
        //set path of file
        //var fileName = Path.Combine(FileSystem.CacheDirectory, $"workout_stat_{DateTime.Now:yyyyMMdd_hhmmss}.jpg");

        HelperClass.ShareImage(await ImageStream.OpenReadAsync(), firebaseEventName: "exercise_stats_share");

        }
        catch (Exception ex)
        {

        }
    }

}
