#pragma warning disable 1591
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: global::Android.Runtime.ResourceDesignerAttribute("DrMuscleWear.Resource", IsApplication=true)]

namespace DrMuscleWear
{
	
	
	[global::System.CodeDom.Compiler.GeneratedCodeAttribute("Xamarin.Android.Build.Tasks", "13.2.2.120")]
	public partial class Resource
	{
		
		static Resource()
		{
			global::Android.Runtime.ResourceIdManager.UpdateIdValues();
		}
		
		public static void UpdateIdValues()
		{
		}
		
		public partial class Attribute
		{
			
			static Attribute()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Attribute()
			{
			}
		}
		
		public partial class Dimension
		{
			
			// aapt resource value: 0x7F010000
			public const int box_inset_layout_padding = 2130771968;
			
			// aapt resource value: 0x7F010001
			public const int inner_frame_layout_padding = 2130771969;
			
			static Dimension()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Dimension()
			{
			}
		}
		
		public partial class Id
		{
			
			// aapt resource value: 0x7F02000B
			public const int btnfinishandSaveWorkout = 2130837515;
			
			// aapt resource value: 0x7F02000A
			public const int btnfinishExercise = 2130837514;
			
			// aapt resource value: 0x7F020006
			public const int btnHide = 2130837510;
			
			// aapt resource value: 0x7F020007
			public const int btnNextExercise = 2130837511;
			
			// aapt resource value: 0x7F020000
			public const int BtnRepsLess = 2130837504;
			
			// aapt resource value: 0x7F020001
			public const int BtnRepsMore = 2130837505;
			
			// aapt resource value: 0x7F020008
			public const int btnSave = 2130837512;
			
			// aapt resource value: 0x7F020009
			public const int btnSaveSet = 2130837513;
			
			// aapt resource value: 0x7F020002
			public const int BtnWeightLess = 2130837506;
			
			// aapt resource value: 0x7F020003
			public const int BtnWeightMore = 2130837507;
			
			// aapt resource value: 0x7F02000C
			public const int dismiss_overlay = 2130837516;
			
			// aapt resource value: 0x7F02000D
			public const int groupPicker = 2130837517;
			
			// aapt resource value: 0x7F020004
			public const int GroupSaveset = 2130837508;
			
			// aapt resource value: 0x7F020005
			public const int GroupTimer = 2130837509;
			
			// aapt resource value: 0x7F02000E
			public const int rect_layout = 2130837518;
			
			// aapt resource value: 0x7F02000F
			public const int round_layout = 2130837519;
			
			// aapt resource value: 0x7F020010
			public const int scroll = 2130837520;
			
			// aapt resource value: 0x7F020011
			public const int spinner1 = 2130837521;
			
			// aapt resource value: 0x7F020012
			public const int txtExerciseName = 2130837522;
			
			// aapt resource value: 0x7F020013
			public const int txtLoading = 2130837523;
			
			// aapt resource value: 0x7F020014
			public const int txtRepsValue = 2130837524;
			
			// aapt resource value: 0x7F020015
			public const int txtTimer = 2130837525;
			
			// aapt resource value: 0x7F020016
			public const int txtWaiting = 2130837526;
			
			// aapt resource value: 0x7F020017
			public const int txtWeightValue = 2130837527;
			
			// aapt resource value: 0x7F020018
			public const int watch_view_stub = 2130837528;
			
			static Id()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Id()
			{
			}
		}
		
		public partial class Layout
		{
			
			// aapt resource value: 0x7F030000
			public const int activity_main = 2130903040;
			
			// aapt resource value: 0x7F030001
			public const int custom_spinner_adapter = 2130903041;
			
			// aapt resource value: 0x7F030002
			public const int rect_layout = 2130903042;
			
			// aapt resource value: 0x7F030003
			public const int round_layout = 2130903043;
			
			static Layout()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Layout()
			{
			}
		}
		
		public partial class Mipmap
		{
			
			// aapt resource value: 0x7F040000
			public const int ic_launcher = 2130968576;
			
			static Mipmap()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private Mipmap()
			{
			}
		}
		
		public partial class String
		{
			
			// aapt resource value: 0x7F050000
			public const int app_name = 2131034112;
			
			// aapt resource value: 0x7F050001
			public const int finish_exercise = 2131034113;
			
			// aapt resource value: 0x7F050002
			public const int finish_workout = 2131034114;
			
			// aapt resource value: 0x7F050003
			public const int hello_world = 2131034115;
			
			// aapt resource value: 0x7F050004
			public const int hide = 2131034116;
			
			// aapt resource value: 0x7F050005
			public const int loading = 2131034117;
			
			// aapt resource value: 0x7F050006
			public const int minus_symbol = 2131034118;
			
			// aapt resource value: 0x7F050007
			public const int next_exercise = 2131034119;
			
			// aapt resource value: 0x7F050008
			public const int plus_symbol = 2131034120;
			
			// aapt resource value: 0x7F050009
			public const int reps = 2131034121;
			
			// aapt resource value: 0x7F05000A
			public const int rest = 2131034122;
			
			// aapt resource value: 0x7F05000B
			public const int save = 2131034123;
			
			// aapt resource value: 0x7F05000C
			public const int save_set = 2131034124;
			
			// aapt resource value: 0x7F05000D
			public const int waiting = 2131034125;
			
			// aapt resource value: 0x7F05000E
			public const int weight = 2131034126;
			
			static String()
			{
				global::Android.Runtime.ResourceIdManager.UpdateIdValues();
			}
			
			private String()
			{
			}
		}
	}
}
#pragma warning restore 1591
